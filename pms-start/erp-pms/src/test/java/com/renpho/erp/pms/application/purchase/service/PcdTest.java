package com.renpho.erp.pms.application.purchase.service;

import cn.hutool.json.JSONUtil;
import com.renpho.erp.pms.Infrastructure.feign.product.ProductLookup;
import com.renpho.erp.pms.adapter.web.controller.shipplanchangeorder.ShipPlanChangeOrderController;
import com.renpho.erp.pms.adapter.web.controller.shipplanchangeorder.cmd.ShipmentPlanChangeInDetailCmd;
import com.renpho.erp.pms.adapter.web.controller.shipplanchangeorder.cmd.ShipmentPlanChangeOrderCmd;
import com.renpho.erp.pms.adapter.web.controller.shipplanchangeorder.cmd.ShipmentPlanChangeOutDetailCmd;
import com.renpho.erp.pms.adapter.web.controller.shipplanchangeorder.vo.PcdOutPoVO;
import com.renpho.erp.pms.adapter.web.controller.shipplanchangeorder.vo.PcdPageVo;
import com.renpho.erp.pms.application.shipplanchangeorder.ShipmentPlanChangeOrderQueryService;
import com.renpho.erp.pms.application.shipplanchangeorder.ShipmentPlanChangeOrderService;
import com.renpho.erp.pms.domain.product.Product;
import com.renpho.erp.pms.domain.product.ProductId;
import com.renpho.erp.pms.domain.shipplanchangeorder.enums.ChangeReasonEnum;
import com.renpho.erp.pms.domain.shipplanchangeorder.enums.ChangeTypeEnum;
import com.renpho.erp.pms.domain.shipplanchangeorder.enums.ReworkTypeEnum;
import com.renpho.erp.pms.domain.shipplanchangeorder.model.ShipmentPlanChangeOrder;
import com.renpho.erp.pms.domain.shipplanchangeorder.query.PoCanOutCmd;
import com.renpho.erp.pms.domain.shipplanchangeorder.query.ShipmentPlanChangeOrderQuery;
import com.renpho.erp.security.service.PlatformUser;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.AfterEach;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@SpringBootTest
@RunWith(SpringRunner.class)
public class PcdTest {

    @Autowired
    private PurchaseOrderQueryService purchaseOrderQueryService;

    @Autowired
    private ShipPlanChangeOrderController cartController;

    @Autowired
    private ShipmentPlanChangeOrderService shipmentPlanChangeOrderService;

    @Autowired
    private ShipmentPlanChangeOrderQueryService shipmentPlanChangeOrderQueryService;

    @Autowired
    private ProductLookup productLookup;

    @Test
    public void testQuery(){
        Optional<Product> byId = productLookup.findById(new ProductId(202));
        System.out.println(JSONUtil.toJsonStr(byId.get()));
    }

    @Test
//    @Transactional(rollbackFor = Exception.class)
    public void testConfirmPcdOrder(){
        ShipmentPlanChangeOrder detail = shipmentPlanChangeOrderQueryService.detail(11);
        shipmentPlanChangeOrderService.confirmPcdOrder(detail);

        System.out.println("PCD: ");
        System.out.println( JSONUtil.toJsonStr(detail));

//        System.out.println("OLD PO" +  );

//        System.out.println("");
//
//        if (true){
//            throw new RuntimeException("SUCCESS");
//        }
  }


    @Test
    public void testSubmitSinglePcd(){
        ShipmentPlanChangeOrderCmd cmd = new ShipmentPlanChangeOrderCmd();

        cmd.setChangeType(ChangeTypeEnum.SINGLE.getCode());
        cmd.setChangeReason(ChangeReasonEnum.SELL_STOCK.getCode());

        R<List<PcdOutPoVO>> listR = cartController.pcdCanOutPo(new PoCanOutCmd());
        PcdOutPoVO pcdOutPoVO = listR.getData().get(0);

        ArrayList<ShipmentPlanChangeOutDetailCmd> arrayList = new ArrayList<>();
        pcdOutPoVO.getDetailList().forEach(detail->{
            ShipmentPlanChangeOutDetailCmd outDetailCmd = new ShipmentPlanChangeOutDetailCmd();

            outDetailCmd.setPrId(detail.getPrId());
            outDetailCmd.setPrNo(detail.getPrNo());
            outDetailCmd.setPoId(pcdOutPoVO.getPoId());
            outDetailCmd.setPoNo(pcdOutPoVO.getPoNo());
            outDetailCmd.setSalesChannelId(detail.getSalesChannelId());
            outDetailCmd.setStoreId(detail.getStoreId());
            outDetailCmd.setProductId(detail.getProductId());
            outDetailCmd.setPsku(detail.getPsku());
            outDetailCmd.setOperationStaffId(detail.getOperationStaffId());

            outDetailCmd.setPurchaseQty(detail.getPurchaseQty());
            outDetailCmd.setUnReceiveQty(detail.getUnReceiveQty());
            outDetailCmd.setMoveOutQty(detail.getUnReceiveQty());

            outDetailCmd.setPoRemainingQty(pcdOutPoVO.getRemainingQty() - outDetailCmd.getMoveOutQty());
            outDetailCmd.setPoShareQty(pcdOutPoVO.getShareQty());

            arrayList.add(outDetailCmd);
        });
        cmd.setOutDetails(arrayList);


        ArrayList<ShipmentPlanChangeInDetailCmd> inList =  new ArrayList<>();
        arrayList.forEach(detail->{
            ShipmentPlanChangeInDetailCmd outDetailCmd = new ShipmentPlanChangeInDetailCmd();

            outDetailCmd.setReworkCost(BigDecimal.valueOf(100));
            outDetailCmd.setReworkType(ReworkTypeEnum.PACKAGE.getCode());

            outDetailCmd.setSalesChannelId(detail.getSalesChannelId());
            outDetailCmd.setStoreId(detail.getStoreId());
            outDetailCmd.setProductId(detail.getProductId());
            outDetailCmd.setPsku(detail.getPsku());
            outDetailCmd.setOperationStaffId(detail.getOperationStaffId());

            outDetailCmd.setExpectedDeliveryDate(LocalDate.now());
            outDetailCmd.setNumberOfUnitsPerBox(1);
            outDetailCmd.setMoveInQty(detail.getMoveOutQty());

            outDetailCmd.setMoveInShareQty(pcdOutPoVO.getShareQty());

            inList.add(outDetailCmd);
        });
        cmd.setInDetails(inList);

        System.out.println(SecurityUtils.getUserId());

        cartController.submit(cmd);
    }

    @Test
    public void testSaveSinglePcd(){
        ShipmentPlanChangeOrderCmd cmd = new ShipmentPlanChangeOrderCmd();

        cmd.setChangeType(ChangeTypeEnum.SINGLE.getCode());
        cmd.setChangeReason(ChangeReasonEnum.SELL_STOCK.getCode());

        R<List<PcdOutPoVO>> listR = cartController.pcdCanOutPo(new PoCanOutCmd());
        PcdOutPoVO pcdOutPoVO = listR.getData().get(0);

        ArrayList<ShipmentPlanChangeOutDetailCmd> arrayList = new ArrayList<>();
        pcdOutPoVO.getDetailList().forEach(detail->{
            ShipmentPlanChangeOutDetailCmd outDetailCmd = new ShipmentPlanChangeOutDetailCmd();

            outDetailCmd.setPrId(detail.getPrId());
            outDetailCmd.setPrNo(detail.getPrNo());
            outDetailCmd.setPoId(pcdOutPoVO.getPoId());
            outDetailCmd.setPoNo(pcdOutPoVO.getPoNo());
            outDetailCmd.setSalesChannelId(detail.getSalesChannelId());
            outDetailCmd.setStoreId(detail.getStoreId());
            outDetailCmd.setProductId(detail.getProductId());
            outDetailCmd.setPsku(detail.getPsku());
            outDetailCmd.setOperationStaffId(detail.getOperationStaffId());

            outDetailCmd.setPurchaseQty(detail.getPurchaseQty());
            outDetailCmd.setUnReceiveQty(detail.getUnReceiveQty());
            outDetailCmd.setMoveOutQty(detail.getUnReceiveQty());

            arrayList.add(outDetailCmd);
        });
        cmd.setOutDetails(arrayList);


        ArrayList<ShipmentPlanChangeInDetailCmd> inList =  new ArrayList<>();
        arrayList.forEach(detail->{
            ShipmentPlanChangeInDetailCmd outDetailCmd = new ShipmentPlanChangeInDetailCmd();

            outDetailCmd.setReworkCost(BigDecimal.ZERO);
            outDetailCmd.setReworkType("NONE");

            outDetailCmd.setSalesChannelId(detail.getSalesChannelId());
            outDetailCmd.setStoreId(detail.getStoreId());
            outDetailCmd.setProductId(detail.getProductId());
            outDetailCmd.setPsku(detail.getPsku());
            outDetailCmd.setOperationStaffId(detail.getOperationStaffId());

            outDetailCmd.setExpectedDeliveryDate(LocalDate.now());
            outDetailCmd.setNumberOfUnitsPerBox(1);
            outDetailCmd.setMoveInQty(detail.getMoveOutQty());

            inList.add(outDetailCmd);
        });
        cmd.setInDetails(inList);

        System.out.println(SecurityUtils.getUserId());
        cmd.setId(8);

        cartController.save(cmd);

    }

    @Test
    public void testPage(){
        R<Paging<PcdPageVo>> page1 = cartController.page(new ShipmentPlanChangeOrderQuery());

        page1.getData().getRecords().forEach(po->{
            System.out.println("PCD: " + po.getPcdNo());
            System.out.println(JSONUtil.toJsonStr(po));
        });

        }

    @Test
    public void testPOOut(){
        R<List<PcdOutPoVO>> listR = cartController.pcdCanOutPo(new PoCanOutCmd());
        listR.getData().forEach(po->{
            System.out.println("PO: " + po.getPoNo());
            System.out.println("PO: " + po.getPoId());
            System.out.println(JSONUtil.toJsonStr(po));
        });
    }

    @Test
    public void testPOOutWithId(){
        PoCanOutCmd poCanOutCmd = new PoCanOutCmd();
        poCanOutCmd.setPoId(111);
        R<List<PcdOutPoVO>> listR = cartController.pcdCanOutPo(poCanOutCmd);
        listR.getData().forEach(po->{
            System.out.println("PO: " + po.getPoNo());
            System.out.println("PO: " + po.getPoId());
            System.out.println(JSONUtil.toJsonStr(po));
        });
    }



    @Before
    public  void setUp() {
        PlatformUser user = new PlatformUser(4209, 1, "admin", "123456", true, true, true, true, List.of());
        UsernamePasswordAuthenticationToken auth =
                new UsernamePasswordAuthenticationToken(
                        user,
                        null,
                        AuthorityUtils.createAuthorityList("ROLE_ADMIN")
                );
        SecurityContextHolder.getContext().setAuthentication(auth);
    }

    @AfterEach
    void tearDown() {
        SecurityContextHolder.clearContext(); // 避免串污染
    }

}
