error.ts.approval.not-exist=The TS has not been approved, tsNo: {0}.
# TS# Carton label related error messages
carton-label.ts.null=TS order cannot be null
carton-label.ts-no.required=TS number cannot be empty
# TS Outbound order related error messages
TRANSFER_ORDER_OUT_NOT_WMS=The destination warehouse of the current TS order is not a self-built warehouse
FMS_FILE_INFO_ERROR=Exception in getting file information from FMS, please contact the administrator
WMS_OUTBOUND_ITEM_NOT_FOUND=TS item [{0}] did not find the corresponding WMS outbound orderNo
CANCEL_TS_OUTBOUND_ORDER_FAIL=Outbound Order Cancellation Failed. Failure Reason: {0}. Transfer Order Void Failed.
error.ts.update-allowed-only-manually-added=Only manually added TS orders can be modified.
error.ts.status-not-allow-operation=This operation is not allowed in the current state
error.ts.status-not-allow-operation.placeholder=This operation is not allowed in the current state, TS No: {0}, Current Status: {1}
error.ts.logistics-mode-not-found.placeholder=Logistics mode not found for shipping warehouse, TS No: {0}
error.ts.mixed-outbound-type=Only one outbound type is allowed, API outbound and non-API outbound cannot be mixed, API outbound TS No: {0}, Non-API outbound TS No: {1}
export.ts.ts-no=TS#
export.ts.status=Status
export.ts.biz-type=Business Type
export.ts.type=Transfer Type
export.ts.psku=PSKU
export.ts.fnsku=FNSKU
export.ts.psku-name=PSKU Name
export.ts.total-quantity=Total Quantity
export.ts.total-cartons=Total Cartons
export.ts.total-gross-weight=Total Gross Weight
export.ts.total-volume=Total Volume
export.ts.shipping-warehouse=Shipping Warehouse
export.ts.destination-warehouse=Destination Warehouse
export.ts.destination-country=Destination Country/Region
export.ts.destination-warehouse-code=Destination Warehouse Code
export.ts.destination=Destination address
export.ts.trade-terms=Trade Terms
export.ts.sales-channel=Sales Channel
export.ts.owner=Owner
export.ts.store=Store
export.ts.is-borrowed=Borrowing Allowed
export.ts.is-relabel=Relabel
export.ts.borrowed-psku=Lender PSKU
export.ts.borrowed-fnsku=Lender FNSKU
export.ts.borrowed-store=Lender Store
export.ts.borrowed-owner=Lender Owner
export.ts.shipmentId=ShipmentID
export.ts.referenceId=ReferenceID
export.ts.estimated-departure-time=Planned Shipment Time
export.ts.estimated-delivery-time=Planned Delivery Time
export.ts.expected-putaway-time=Expected Putaway Time
export.ts.outbound-no=Outbound Order
export.ts.departure-start-time=Shipment Time
export.ts.order-no=Order No.
export.ts.ref-no=Ref.No
