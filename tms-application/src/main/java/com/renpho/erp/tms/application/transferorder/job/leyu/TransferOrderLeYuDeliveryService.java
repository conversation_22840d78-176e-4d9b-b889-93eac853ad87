package com.renpho.erp.tms.application.transferorder.job.leyu;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.apiproxy.jd.fop.model.instock.QueryInStockPageData;
import com.renpho.erp.apiproxy.lingxing.LingxingResponse;
import com.renpho.erp.apiproxy.lingxing.SystemAccount;
import com.renpho.erp.apiproxy.lingxing.wms.api.LxwmsInboundOrderApi;
import com.renpho.erp.apiproxy.lingxing.wms.model.inboundOrder.GetInboundOrderDetailData;
import com.renpho.erp.apiproxy.lingxing.wms.model.inboundOrder.GetInboundOrderDetailRequest;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderService;
import com.renpho.erp.tms.application.transferorder.job.common.TransferOrderCommonService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestQueryService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestService;
import com.renpho.erp.tms.application.transportrequest.job.common.TransportRequestCommonService;
import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.inbound.InboundRequestHistory;
import com.renpho.erp.tms.domain.inbound.InboundStatus;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transferorder.*;
import com.renpho.erp.tms.domain.transportorder.TransportOrderStatusEnum;
import com.renpho.erp.tms.domain.transportorder.dto.TransportRequestOrderData;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.repository.TransferOrderItemRepository;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderItemRepository;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;
import com.renpho.erp.tms.infrastructure.util.DateUtils;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * TS-目的仓为乐鱼-入库单签收任务.
 *
 * <AUTHOR>
 * @since 2025/8/29
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransferOrderLeYuDeliveryService {

    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransferOrderCommonService transferOrderCommonService;
    private final TransferOrderService transferOrderService;
    private final PushTaskService pushTaskService;

    private final TransferOrderRepository transferOrderRepository;
    private final TransferOrderItemRepository transferOrderItemRepository;

    private final LxwmsInboundOrderApi lxwmsInboundOrderApi;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.LEYU_XLWMS;

    /**
     * TS-目的仓为乐鱼-入库单签收任务.
     */
    @Lock4j(name = "transfer:order:leyu:inbound:delivery:basic")
    //@Transactional(rollbackFor = Exception.class)
    public void createInboundLeYu(List<String> trNoList) {
        try {
            log.info("TS-目的仓为乐鱼的定时器任务开始");

            // 状态是部分签收、已发货
            List<TransferOrderStatus> statusList = List.of(TransferOrderStatus.SHIPPED, TransferOrderStatus.RECEIVING);
            List<TransferOrderData> tsDataList = transferOrderService.selectTsListByStatusList(statusList, warehouseType);

            // 外部参数
            tsDataList = transferOrderCommonService.fillExterWithTsStatus(statusList, tsDataList, trNoList);

            // 生成入库单推送任务
            if (tsDataList != null && !tsDataList.isEmpty()) {
                // 获取ts上下文信息
                Map<Integer, TransferOrder> tsMap = transferOrderService.getTsMapByIds(tsDataList.stream().map(TransferOrderData::getId).toList());

                for (TransferOrderData tsData : tsDataList) {
                    try {
                        TransferOrder ts = tsMap.get(tsData.getId());
                        if (ts == null) {
                            log.error("TS-目的仓为乐鱼-入库单签收任务生成异常, ts上下文找不到, tsId={}", tsData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (warehouseType != ts.findWarehouseProviderType()) {
                            continue;
                        }

                        // 生成推送任务
                        PushTask trPushTask = pushTaskService.inbound(ts, PushTaskStatus.PENDING,0,null);

                        // 执行签收任务
                        pushTaskService.execute(() -> preHandlerDelivery(ts, trPushTask), trPushTask);
                    } catch (Exception e) {
                        log.error("TS-目的仓为乐鱼-入库单签收任务异常, 入库单推送任务生成失败, tsId={}", tsData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TS-目的仓为乐鱼-入库单签收任务", e);
        }
    }

    /**
     * 处理签收-前置
     */
    private Boolean preHandlerDelivery(TransferOrder ts, PushTask trPushTask){
        // 验证shipment Id是否为空
        if(StringUtils.isBlank(ts.getShipmentId())){
            log.error("TS-目的仓为乐鱼-入库单签收任务异常, shipment Id为空, tsId={}", ts.getId());
            throw new RuntimeException("TS-目的仓为乐鱼-入库单签收任务异常, shipment Id为空, tsId=" + ts.getId());
        }

        GetInboundOrderDetailRequest queryDto = new GetInboundOrderDetailRequest();
        queryDto.setInboundOrderNoList(List.of(ts.getShipmentId()));

        String account = transferOrderCommonService.getConsumerCode(ts.getDestWarehouse().getId().id());
        if (account == null) {
            log.error("TS-目的仓为乐鱼-执行入库单任务异常, 仓库编码找不到, tsId={}", ts.getId());
            return null;
        }
        SystemAccount systemAccount = new SystemAccount();
        systemAccount.setAccount(account);

        log.info("TS-目的仓为乐鱼-签收任务执行-请求, 参数={}", JSON.toJSONString(queryDto));
        R<LingxingResponse<List<GetInboundOrderDetailData>>> result = lxwmsInboundOrderApi.getInboundOrderDetail(systemAccount, queryDto);
        log.info("TS-目的仓为乐鱼-签收任务执行-响应, 响应={}", JSON.toJSONString(result));

        Integer resultCode = 200;
        Object body = null;
        if (result != null) {
            resultCode = result.getData().getCode();
            body = result.getData();

            if (result.isSuccess()) {
                // 记录请求历史
                InboundRequestHistory hisRecord = inboundRequestHistoryService.add(ts, WarehouseProviderType.LEYU_XLWMS, null, queryDto, result.getData().getCode(), null, result);

                LingxingResponse<List<GetInboundOrderDetailData>> listInstock = result.getData();

                // 签收处理
                SpringUtil.getBean(this.getClass()).handlerDelivery(listInstock, ts, trPushTask, hisRecord);
            } else {
                // 记录请求历史
                inboundRequestHistoryService.add(ts, WarehouseProviderType.LEYU_XLWMS, null, queryDto, result.getData().getCode(), null, result);
                throw new DingTalkWarning("API获取入库单失败，原因：%s".formatted(result.getMessage()));
            }
        }
        return true;
    }

    /**
     * 处理签收
     */
    @Transactional(rollbackFor = Exception.class)
    public void handlerDelivery(LingxingResponse<List<GetInboundOrderDetailData>> listInstock, TransferOrder ts, PushTask trPushTask, InboundRequestHistory hisRecord) {
        if(listInstock == null || listInstock.getData().isEmpty()){
            return;
        }

        List<TransferOrderItem> items = ts.getItems();
        GetInboundOrderDetailData vo = listInstock.getData().get(0);
        List<GetInboundOrderDetailData.InboundSkuVO> details = vo.getInboundSkuVOList();

        // 签收数据 -> 2-收货中=部分签收，3-已收货、4-已上架=全部签收， 乐鱼不存在部分签收状态
        switch (vo.getStatus()) {
            case 2:
                // 收货中目前不处理
                break;
            case 3:
                // 已收货(签收完成)
                for(TransferOrderItem itemTs : items){
                    for(GetInboundOrderDetailData.InboundSkuVO detail : details){
                        if(itemTs.getFnSku().equals(detail.getSku())){  // TODO 不确定是Fnsku还是sku
                            partSignIn(itemTs, detail, ts, vo, hisRecord, InboundStatus.WORKING);
                        }
                    }
                }
                transferOrderRepository.updateById(ts);
                transferOrderItemRepository.update(ts);
                break;
            case 4:
                // 已上架(签收完成)
                for(TransferOrderItem tsItem : items){
                    for(GetInboundOrderDetailData.InboundSkuVO detail : details){
                        if(tsItem.getFnSku().equals(detail.getSku())){  // TODO 不确定是Fnsku还是sku
                            partSignIn(tsItem, detail, ts, vo, hisRecord, InboundStatus.WORKING);

                            // 计算差异数量 = 签收数量 - 计划发货数量
                            Integer diffQuantity = detail.getReceivedQuantity() - tsItem.getQty();
                            tsItem.setReceivedDiscrepancy(diffQuantity);

                            // 上架差异数量
                            tsItem.setPutawayDiscrepancy(tsItem.getPutawayQty() - tsItem.getQty());
                        }
                    }
                }
                ts.setSyncApiStatus(SyncApiStatus.SUCCESS);
                ts.setStatus(TransferOrderStatus.RECEIVED);  // 没有分已签收跟已完成，直接已签收
                transferOrderRepository.updateById(ts);
                transferOrderItemRepository.update(ts);

                // 同步TS单状态，有差异数量已签收，没有差异数量已完成
                // transportRequestCommonService.syncTransportRequestStatus(diffQuantity, tr);

                // 维护 tr上架的生命周期
                pushTaskService.putaway(ts, PushTaskStatus.SUCCESS, 0, null);
                break;
            default:
                break;
        }
    }

    /**
     * 部分签收
     */
    private void partSignIn(TransferOrderItem itemTs, GetInboundOrderDetailData.InboundSkuVO detail, TransferOrder ts, GetInboundOrderDetailData vo, InboundRequestHistory hisRecord, InboundStatus inboundStatus){
        itemTs.setReceivedQty(detail.getReceivedQuantity());
        itemTs.setPutawayQty(detail.getReceivedQuantity());

        // TODO 首次签收推送签收消息到FMS
        // transferOrderService.firstReceiveSendReceiveMsgToFms(ts.getTsNo(),  itemTs.getReceivedStartTime()== null);

        if(StringUtils.isNotBlank(vo.getReceivedEndTime())){
            // 因为没有部分签收，所以开始时间跟结束时间一致
            LocalDateTime receivedTime = DateUtils.toUtcLocalDateTime(vo.getReceivedEndTime());
            itemTs.setReceivedStartTime(receivedTime);
            itemTs.setReceivedEndTime(receivedTime);

            // 入库单全部签收的，则TR单出运状态更新“签收完成”
            //tr.setShipStatus(TransportOrderStatusEnum.COMPLETED);
        }

        // 入库单状态签收中的，则TS单出运状态由“已发货”更新为“部分签收”
        if (TransferOrderStatus.SHIPPED == ts.getStatus()) {
            ts.setStatus(TransferOrderStatus.RECEIVING);
        }

        // 记录record
        Integer receiveQty = detail.getReceivedQuantity();
        LocalDateTime signTime = LocalDateTime.now();
        LocalDateTime putWayTime = LocalDateTime.now();
        if(InboundStatus.WORKING == inboundStatus){
            signTime = itemTs.getReceivedStartTime();
            putWayTime = itemTs.getReceivedEndTime();
        } else if(InboundStatus.FINISH == inboundStatus){
            signTime = DateUtils.toUtcLocalDateTime(vo.getReceivedEndTime());
            putWayTime = DateUtils.toUtcLocalDateTime(vo.getShelfEndTime());
        }

        // 记录record
        transferOrderCommonService.doRecord(warehouseType, receiveQty, receiveQty, signTime, putWayTime, itemTs, hisRecord, inboundStatus);
    }

}
