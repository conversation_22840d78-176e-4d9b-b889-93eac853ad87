package com.renpho.erp.tms.application.discrepancy;

import com.renpho.erp.ims.client.feign.inventory.command.InventoryDiscrepancyAddCommand;
import com.renpho.erp.tms.application.transportorder.TransportOrderService;
import com.renpho.erp.tms.domain.transferorder.TransferOrder;
import com.renpho.erp.tms.domain.transferorder.TransferOrderStatus;
import com.renpho.erp.tms.domain.transferorder.TransferOrderStatusHistoryRepository;
import com.renpho.erp.tms.domain.transportorder.TransportOrderStatusEnum;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.converter.TransportRequestConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;
import com.renpho.erp.tms.infrastructure.remote.inventory.RemoteInventoryDiscrepancyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 差异处理服务
 * <AUTHOR>
 * @since 2025/8/30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DiscrepancyService {
    private final TransferOrderStatusHistoryRepository transferOrderStatusHistoryRepository;
    private final TransferOrderConverter transferOrderConverter;
    private final RemoteInventoryDiscrepancyService remoteInventoryDiscrepancyService;
    private final TransportRequestConverter transportRequestConverter;
    private final TransportRequestOrderRepository transportRequestOrderRepository;
    private final TransportOrderService transportOrderService;


    /**
     * 处理调拨单签收差异，
     * @param ts 调拨单
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleTsDiscrepancy(TransferOrder ts) {
        //TS单为已签收，判断是否有签收差异？如果有差异，则生成差异单
        if (ts.haveDiscrepancy()) {
            //有签收差异，调用IMS，创建差异单
            //组装参数
            InventoryDiscrepancyAddCommand cmd = transferOrderConverter.toInventoryDiscrepancyAddCommand(ts);
            //调用IMS，创建差异单
            Map<String, String> resMap = remoteInventoryDiscrepancyService.createInventoryDiscrepancy(cmd);
            //差异单号
            if (resMap.containsKey(ts.getTsNo())) {
                String discrepancyNo = resMap.get(ts.getTsNo());
                //更新TR单
                ts.setDiscrepancyNo(discrepancyNo);
                ts.setStatus(TransferOrderStatus.CLOSED);
                transferOrderStatusHistoryRepository.addHistoryStatus(ts.getId().id(), ts.getTsNo(), TransferOrderStatus.CLOSED.getValue());
            } else {
                log.error("差异单创建成功，但未找到对应单据的差异单号：{}", ts.getTsNo());
            }
        } else {
            //无差异，则更新TS单状态为“已关闭"
            ts.setStatus(TransferOrderStatus.CLOSED);
            transferOrderStatusHistoryRepository.addHistoryStatus(ts.getId().id(), ts.getTsNo(), TransferOrderStatus.CLOSED.getValue());
        }
    }




    /**
     * 处理TR单签收差异，
     * @param tr TR单
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleTrDiscrepancy(TransportRequest tr) {
        //有签收差异，调用IMS，创建差异单
        //组装参数
        InventoryDiscrepancyAddCommand cmd = transportRequestConverter.buildInventoryDiscrepancyAddCommand(tr);
        //调用IMS，创建差异单
        Map<String, String> resMap = remoteInventoryDiscrepancyService.createInventoryDiscrepancy(cmd);
        //差异单号
        if (resMap.containsKey(tr.getTrNo())) {
            String discrepancyNo = resMap.get(tr.getTrNo());
            //更新TR单
            tr.setDiscrepancyNo(discrepancyNo);
            tr.setShipStatus(TransportOrderStatusEnum.COMPLETED);
            //更新TR单
            transportRequestOrderRepository.updateById(tr);
            transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.COMPLETED);
        } else {
            log.error("差异单创建成功，但未找到对应单据的差异单号：{}", tr.getTrNo());
        }
    }
}
