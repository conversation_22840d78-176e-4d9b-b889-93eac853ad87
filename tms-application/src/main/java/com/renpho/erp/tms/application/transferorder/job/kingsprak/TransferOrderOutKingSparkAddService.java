package com.renpho.erp.tms.application.transferorder.job.kingsprak;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.apiproxy.eccang.api.YunwmsInFbaApi;
import com.renpho.erp.apiproxy.eccang.yunwms.SystemAccount;
import com.renpho.erp.apiproxy.eccang.yunwms.model.fba.CreateOrEditFbaOrderRequest;
import com.renpho.erp.apiproxy.eccang.yunwms.model.fba.CreateOrEditFbaOrderResponse;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderService;
import com.renpho.erp.tms.application.transferorder.job.common.TransferOrderCommonService;

import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transferorder.TransferOrder;

import com.renpho.erp.tms.domain.transferorder.TransferOrderItem;
import com.renpho.erp.tms.domain.transferorder.TransferOrderRepository;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.inbound.EccangInboundClient;

import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


/**
 * TS-目的仓为KingSpark-创建出库单.
 *
 * <AUTHOR>
 * @since 2025/8/30
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransferOrderOutKingSparkAddService {

    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransferOrderCommonService transferOrderCommonService;
    private final TransferOrderService transferOrderService;
    private final TransferOrderRepository transferOrderRepository;

    private final YunwmsInFbaApi yunwmsInFbaApi;

    /**
     * TS-目的仓为KingSpark-创建出库单.
     */
    @Lock4j(name = "transfer:order:kingspark:outbound:add:basic")
    @Transactional(rollbackFor = Exception.class)
    public Boolean createOutboundOrder(TransferOrder ts) {
        String warehouseCode = transferOrderCommonService.getWarehouseCode(ts.getDestWarehouse().getId().id());
        if (warehouseCode == null) {
            log.error("TS-目的仓为KingSpark-创建出库单异常, 仓库编码找不到, tsId={}", ts.getId());
            return false;
        }

        String consumerCode = transferOrderCommonService.getConsumerCode(ts.getDestWarehouse().getId().id());
        if (consumerCode == null) {
            log.error("TS-目的仓为KingSpark的定时器任务异常, 无法获取客户编码, tsId={}", ts.getId());
            return false;
        }

        SystemAccount systemAccount = new SystemAccount();
        systemAccount.setAccount(consumerCode);

        CreateOrEditFbaOrderRequest request = new CreateOrEditFbaOrderRequest();
        request.setReference_number(ts.getTsNo());  // TS单号
        request.setFba_type(2); // 2 订单

        // TODO 销售渠道Amazon并且目的仓是平台仓的，传； 传啥?  先传目的仓Code试一下
        request.setFba_w_code(ts.getDestWarehouseCode());

        request.setAmazon_shipment(ts.getShipmentId());
        request.setAmazon_reference(ts.getReferenceId());

        // TODO KP 是不是这个值?
        request.setTransit_warehouse_code("KP");

        request.setTo_warehouse_code(ts.getShippingWarehouseCode()); // FBA订单:发货仓CODE
        request.setStock_type(0);  // 0以仓库为准
        request.setSm_code(ts.getLogisticsMode().getCode());  // 物流方式的物流服务代码

        // 1、VC的：{ VC单+PSKU/数量+ASN号 + 一个外箱上面需贴两张外箱标签：cartonLabels和ASIN Labels }；拼接给，空格隔开
        // 2、其他的不传
        // TODO 坐等demo
        request.setFba_remarks(null);

        List<TransferOrderItem> items =ts.getItems();
        List<CreateOrEditFbaOrderRequest.Product> products = new ArrayList<>();
        for (TransferOrderItem item : items) {
            int boxQty = item.getBoxQty().intValue();
            for(int i=1;i<=boxQty;i++){
                CreateOrEditFbaOrderRequest.Product product = new CreateOrEditFbaOrderRequest.Product();
                product.setBox_no(i);  // 递增叠加
                product.setProduct_barcode(item.getFnSku());
                product.setQuantity(item.getQuantityPerBox());
                products.add(product);
            }
        }
        request.setProducts(products);

        // TODO fba_address 坐等sdk补充

        log.info("TS-目的仓为KingSpark-创建出库单, 参数={}", JSON.toJSONString(request));
        R<CreateOrEditFbaOrderResponse>  result = yunwmsInFbaApi.createOrEditFbaOrder(systemAccount, request);
        log.info("TS-目的仓为KingSpark-创建出库单, 结果={}", JSON.toJSONString(result));
        if (result != null) {
            if(result.getData() != null){
                CreateOrEditFbaOrderResponse data = result.getData();
                if (data.getAsk().equals("Success")) {
                    // 记录请求历史
                    inboundRequestHistoryService.add(ts, WarehouseProviderType.KING_SPARK_YUNWMS, null, request, 200, null, result);

                    // TODO 确认是不是这个字段
                    // String outboundCode = data.getCode();

                } else {
                    // 记录日志
                    inboundRequestHistoryService.add(ts, WarehouseProviderType.KING_SPARK_YUNWMS, null, request, 200, null, result);
                    throw new DingTalkWarning("API创建出库单失败，原因：%s".formatted(result.getData().getMessage()));
                }
            } else {
                // 记录日志
                inboundRequestHistoryService.add(ts, WarehouseProviderType.KING_SPARK_YUNWMS, null, request, 200, null, result);
                throw new DingTalkWarning("API创建出库单失败，原因：%s".formatted(result.getMessage()));
            }
        }

        return true;
    }

}
