package com.renpho.erp.tms.application.transferorder;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.renpho.erp.apiproxy.amazon.model.ShopAccount;
import com.renpho.erp.apiproxy.amazon.model.fbaInbound.GetShipmentsV0Request;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.tms.application.discrepancy.DiscrepancyService;
import com.renpho.erp.tms.application.inbound.InboundRecordQueryService;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.domain.inbound.*;
import com.renpho.erp.tms.domain.store.Store;
import com.renpho.erp.tms.domain.transferorder.*;
import com.renpho.erp.tms.domain.transportrequest.PushTask;
import com.renpho.erp.tms.domain.transportrequest.PushTaskStatus;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.erp.tms.infrastructure.common.constant.CommonConstant;
import com.renpho.erp.tms.infrastructure.persistence.inbound.po.converter.InboundRecordConverter;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.repository.TransferOrderItemRepository;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.ShopAccountBuilder;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.inbound.AmazonInboundClient;
import com.renpho.erp.tms.infrastructure.remote.store.repository.StoreLookup;
import com.renpho.karma.dto.R;
import io.swagger.client.model.fbaInboundV0.*;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/8/26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransferOrderFBAInboundService {
    private final TransferOrderQueryService transferOrderQueryService;
    private final StoreLookup storeLookup;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final InboundRecordRepository inboundRecordRepository;
    private final InboundRecordQueryService inboundRecordQueryService;
    private final InboundRecordConverter inboundRecordConverter;
    private final PushTaskService pushTaskService;
    private final AmazonInboundClient amazonInboundClient;
    private final TransferOrderRepository transferOrderRepository;
    private final TransferOrderItemRepository transferOrderItemRepository;
    private final TransferOrderStatusHistoryRepository transferOrderStatusHistoryRepository;
    private final DiscrepancyService discrepancyService;

    //FBA签收状态
    final List<ShipmentStatus> FBA_RECEIVED_STATUS = Arrays.asList(ShipmentStatus.DELIVERED, ShipmentStatus.CHECKED_IN,
            ShipmentStatus.RECEIVING, ShipmentStatus.CLOSED);
    //FBA上架状态
    final List<ShipmentStatus> FBA_ON_SHELVES_STATUS = Arrays.asList(ShipmentStatus.RECEIVING, ShipmentStatus.CLOSED);


    public void pullShipmentsFromFBAForTS(List<String> tsNos, List<TransferOrderId> tsIds, String syncApiStatus) {

        //“已发货”和“部分签收”状态的TS单
        List<TransferOrder> fbaTsList = transferOrderQueryService.findByWarehouseProviderType(
                WarehouseProviderType.FBA,
                tsIds,
                tsNos,
                List.of(TransferOrderStatus.SHIPPED, TransferOrderStatus.RECEIVING),
                syncApiStatus
        ).stream().filter(ts -> StringUtil.isNotBlank(ts.getShipmentId())).toList();

        //按照Store进行分组
        Map<Store, List<TransferOrder>> shipmentsGroupByStore = CollectionUtil.emptyIfNull(fbaTsList).stream()
                .collect(Collectors.groupingBy(TransferOrder::getStore));
        for (Map.Entry<Store, List<TransferOrder>> entry : shipmentsGroupByStore.entrySet()) {
            List<TransferOrder> trs = entry.getValue();
            Store store = entry.getKey();
            StoreAuthorizationVo authorization = storeLookup.getAuthorization(store);
            //获取入库单详情
            for (TransferOrder ts : trs) {
                fetchAndProcessShipmentDetails(ts, authorization.getAuthorization());
            }
        }
    }

    /**
     * 组装获取入库单的参数
     *
     * @param shipmentId    shipmentId
     * @param authorization 授权信息
     * @return 获取入库单的参数
     */
    private GetShipmentsV0Request buildGetGetShipmentsV0Request(String shipmentId,
                                                                StoreAuthorizationVo.Authorization authorization) {
        GetShipmentsV0Request req = new GetShipmentsV0Request();
        req.setMarketplaceId(authorization.getAmzMarketplaceId());
        req.setQueryType("SHIPMENT");
        req.setShipmentIdList(List.of(shipmentId));
        req.setShipmentStatusList(FBA_RECEIVED_STATUS.stream().map(ShipmentStatus::getValue).toList());
        return req;
    }

    /**
     * 获取FBA平台仓入库单详情
     *
     * @param ts            TS
     * @param authorization 授权
     */
    private void fetchAndProcessShipmentDetails(TransferOrder ts,
                                                StoreAuthorizationVo.Authorization authorization) {
        ShopAccount shopAccount = ShopAccountBuilder.buildAmzShopAccount(authorization);
        GetShipmentsV0Request req = buildGetGetShipmentsV0Request(ts.getShipmentId(), authorization);

        try {
            // 保存请求记录
            InboundRequestHistory history = inboundRequestHistoryService.add(ts, WarehouseProviderType.FBA, null, req);
            PushTask inboundPushTask = pushTaskService.inbound(ts, PushTaskStatus.PENDING, 0, null);
            // 调用详情接口，获取详情（首次调用）
            R<GetShipmentsResponse> ret = pushTaskService.execute(() -> {
                //第一次请求
                R<GetShipmentsResponse> r = amazonInboundClient.getShipments(shopAccount, req);
                //将异常抛出才能触发重试机制
                if (!r.isSuccess()) {
                    log.error("拉取【FBA-入库单列表】接口异常：, tsNo={}", ts.getTsNo());
                    inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                    throw new RuntimeException("API获取入库单失败，原因：%s".formatted(r.getMessage()));
                }
                return r;
            }, inboundPushTask);

            inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null, ret.getData());

            GetShipmentsResult payload = ret.getData().getPayload();
            this.handleShipmentResult(shopAccount, payload.getShipmentData(), ts, inboundPushTask);
            // 分页处理 nextToken
            String nextToken = payload.getNextToken();
            while (StringUtil.isNotBlank(nextToken)) {
                GetShipmentsResult nextPayload = this.getShipmentsByNextToken(ts, shopAccount,
                        authorization.getAmzMarketplaceId(), nextToken, inboundPushTask);
                this.handleShipmentResult(shopAccount, nextPayload.getShipmentData(), ts, inboundPushTask);
                nextToken = nextPayload.getNextToken();
            }

        } catch (Exception e) {
            log.error("拉取【FBA-入库单列表】失败, tsNo={}", ts.getTsNo(), e);
        }
    }

    /**
     * 通过下一个token获取入库单
     *
     * @param shopAccount     店铺信息
     * @param nextToken       下一个token
     * @param inboundPushTask 入库推送任务
     * @return 入库单列表
     */
    private GetShipmentsResult getShipmentsByNextToken(TransferOrder ts,
                                                       ShopAccount shopAccount,
                                                       String amzMarketplaceId,
                                                       String nextToken,
                                                       PushTask inboundPushTask) {
        GetShipmentsV0Request req = new GetShipmentsV0Request();
        req.setMarketplaceId(amzMarketplaceId);
        req.setQueryType("NEXT_TOKEN");
        req.setNextToken(nextToken);

        // 保存请求记录
        InboundRequestHistory history = inboundRequestHistoryService.add(ts, WarehouseProviderType.FBA, null, req);

        // 调用详情接口，获取详情（首次调用）
        R<GetShipmentsResponse> ret = pushTaskService.execute(() -> {
            R<GetShipmentsResponse> r = amazonInboundClient.getShipments(shopAccount, req);
            //将异常抛出才能触发重试机制
            if (!r.isSuccess()) {
                log.error("调用【FBA-分页查询入库单】接口异常：, tsNo={}，nextToken：【{}】, 异常信息：{}", ts.getTsNo(), nextToken, r);
                inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                throw new RuntimeException("API获取入库单失败，原因：%s".formatted(r.getMessage()));
            }
            return r;
        }, inboundPushTask);

        inboundRequestHistoryService.update(history.getId(), Integer.parseInt(ret.getCode()), null, ret.getData());

        return ret.getData().getPayload();
    }

    /**
     * 根据Shipment获取ShipmentsItems
     *
     * @param shopAccount         店铺账号
     * @param inboundShipmentList shipmentList
     * @param ts                  tr
     * @param inboundPushTask     入库推送任务
     */
    private void handleShipmentResult(ShopAccount shopAccount,
                                      InboundShipmentList inboundShipmentList,
                                      TransferOrder ts,
                                      PushTask inboundPushTask) {

        if (CollectionUtils.isEmpty(inboundShipmentList)) {
            return;
        }

        for (InboundShipmentInfo shipmentData : inboundShipmentList) {
            ShipmentStatus shipmentStatus = shipmentData.getShipmentStatus();
            String shipmentId = shipmentData.getShipmentId();

            // 保存请求记录
            InboundRequestHistory history = inboundRequestHistoryService.add(ts, WarehouseProviderType.FBA, null, shipmentId);

            // 调用详情接口，获取详情（首次调用）
            R<GetShipmentItemsResponse> ret = pushTaskService.execute(() -> {
                R<GetShipmentItemsResponse> r = amazonInboundClient.getShipmentItemsByShipmentId(shopAccount, shipmentId);
                //将异常抛出才能触发重试机制
                if (!r.isSuccess()) {
                    log.error("获取【FBA-入库单Item】接口异常：, tsNo={}, 异常信息：{}", ts.getTsNo(), r);
                    inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                    throw new RuntimeException("API获取入库单失败，原因：%s".formatted(r.getMessage()));
                }
                return r;
            }, inboundPushTask);

            // 保存请求记录
            inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null, ret.getData());

            GetShipmentItemsResult itemsResult = ret.getData().getPayload();
            LocalDateTime triggerTime = LocalDateTime.now();
            SpringUtil.getBean(this.getClass()).handleShipmentItems(itemsResult.getItemData(), shipmentStatus, ts, triggerTime, history.getId());

        }
    }

    /**
     * 处理 FBA 类型入库的 ShipmentItem 数据，包括签收与上架的记录生成及 TS 单状态同步。
     *
     * @param itemData         分页获取的 ShipmentItem 列表
     * @param shipmentStatus   当前处理的货件状态
     * @param ts               当前处理的 TS 单对象
     * @param triggerTime      当前操作的触发时间（如接口返回的操作时间）
     * @param requestHistoryId 请求历史记录 ID，用于标记入库记录归属
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleShipmentItems(InboundShipmentItemList itemData,
                                    ShipmentStatus shipmentStatus,
                                    TransferOrder ts,
                                    LocalDateTime triggerTime,
                                    InboundRequestHistoryId requestHistoryId) {
        if (CollectionUtils.isEmpty(itemData)) {
            log.warn("分页的ShipmentItems为空");
            return;
        }

        //将itemData按照FulfillmentNetworkSKU进行分组，返回一个Map，key为FulfillmentNetworkSKU，value为InboundShipmentItem
        Map<String, InboundShipmentItem> fbaDataMap = itemData.stream().collect(
                Collectors.toMap(
                        InboundShipmentItem::getFulfillmentNetworkSKU,
                        Function.identity(),
                        (v, v2) -> v
                )
        );

        for (TransferOrderItem tsItem : ts.getItems()) {
            if (!fbaDataMap.containsKey(tsItem.getFnSku())) {
                continue;
            }
            InboundShipmentItem shipmentItem = fbaDataMap.get(tsItem.getFnSku());

            // 查询上一次记录，用于差异计算
            InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tsItem).orElse(null);

            processItem(shipmentItem, shipmentStatus, tsItem, triggerTime, requestHistoryId, lastRecord);
        }

        //处理完签收逻辑后，处理 TS 和 TS Item 的更新
        transferOrderItemRepository.batchUpdate(ts.getItems());


        // 处理签收逻辑（如果当前状态属于签收状态）
        if (FBA_RECEIVED_STATUS.contains(shipmentStatus)) {
            ts.setStatus(TransferOrderStatus.RECEIVING);
            transferOrderStatusHistoryRepository.addHistoryStatus(ts.getId().id(), ts.getTsNo(), TransferOrderStatus.RECEIVING.getValue());
            // 若货件状态为 CLOSED，说明已完成签收与上架
            if (ShipmentStatus.CLOSED.equals(shipmentStatus)) {
                ts.setStatus(TransferOrderStatus.RECEIVED);
                transferOrderStatusHistoryRepository.addHistoryStatus(ts.getId().id(), ts.getTsNo(), TransferOrderStatus.RECEIVED.getValue());

                // 处理TS单的签收差异
                discrepancyService.handleTsDiscrepancy(ts);
            }
        }

        ts.setSyncApiStatus(SyncApiStatus.SUCCESS);
        //更新TS单
        transferOrderRepository.updateById(ts);
    }

    /**
     * 处理单个 ShipmentItem 的签收与上架逻辑，并记录入库信息。
     */
    private void processItem(InboundShipmentItem item,
                             ShipmentStatus shipmentStatus,
                             TransferOrderItem tsItem,
                             LocalDateTime triggerTime,
                             InboundRequestHistoryId requestHistoryId,
                             InboundRecord lastRecord) {

        // 构建新的入库记录对象
        InboundRecord record = inboundRecordConverter.transferOrderItemToInboundRecord(tsItem);
        record.setWarehouseType(WarehouseProviderType.FBA);
        record.setRequestHistoryId(requestHistoryId);

        Integer receivedQty = tsItem.getQty(); // 实际签收数量默认为发货数量
        Integer putawayQty = Optional.ofNullable(item.getQuantityReceived()).orElse(0); // 上架数量

        boolean haveNewReceived = false, haveNewPutaway = false;
        // 处理签收逻辑（如果当前状态属于签收状态）
        if (FBA_RECEIVED_STATUS.contains(shipmentStatus)) {
            haveNewReceived = handleReceived(record, tsItem, receivedQty, triggerTime, lastRecord);
        }

        // 处理上架逻辑（如果当前状态属于上架状态）
        if (FBA_ON_SHELVES_STATUS.contains(shipmentStatus)) {
            haveNewPutaway = handlePutaway(record, tsItem, putawayQty, triggerTime, lastRecord);
        }

        // 更新 TS Item 的签收和上架数量
        updateItemQuantities(tsItem, receivedQty, putawayQty);

        // 若货件状态为 CLOSED，说明已完成签收与上架
        if (ShipmentStatus.CLOSED.equals(shipmentStatus)) {
            record.setPutawayStatus(InboundStatus.FINISH);
            tsItem.setPutawayEndTime(triggerTime);
            tsItem.calculateTheDifferenceValue(tsItem.getReceivedQty(), tsItem.getPutawayQty());
        }

        // 如果本次存在新增的签收或上架记录，则落库并更新状态
        if (haveNewReceived || haveNewPutaway) {
            // 保存签收/上架记录
            inboundRecordRepository.add(record);
        }
    }

    /**
     * 构建签收信息并更新 TS 单签收状态。
     */
    private boolean handleReceived(InboundRecord record,
                                   TransferOrderItem tsItem,
                                   Integer receivedQty,
                                   LocalDateTime triggerTime,
                                   InboundRecord lastRecord) {
        record.setStatus(InboundStatus.FINISH);
        record.setPutawayStatus(receivedQty > 0 ? InboundStatus.WORKING : InboundStatus.PENDING);

        boolean updated = record.buildReceivedInfo(receivedQty, lastRecord);
        record.setReceivedTime(triggerTime);

        // 如果是首次签收，记录时间
        if (tsItem.checkIsFirstReceived()) {
            tsItem.setReceivedStartTime(triggerTime);
        }

        return updated;
    }

    /**
     * 构建上架信息并更新 TS 单上架状态。
     */
    private boolean handlePutaway(InboundRecord record,
                                  TransferOrderItem tsItem,
                                  Integer putawayQty,
                                  LocalDateTime triggerTime,
                                  InboundRecord lastRecord) {
        boolean updated = record.buildPutawayInfo(putawayQty, lastRecord);
        record.setPutawayTime(triggerTime);
        record.setPutawayStatus(InboundStatus.WORKING);

        // 记录上架完成时间
        tsItem.setReceivedEndTime(triggerTime);
        tsItem.setPutawayStartTime(triggerTime);
        return updated;
    }

    /**
     * 更新 TS Item 的签收和上架数量。
     */
    private void updateItemQuantities(TransferOrderItem tsItem, Integer receivedQty, Integer putawayQty) {
        tsItem.setReceivedQty(receivedQty);
        tsItem.setPutawayQty(putawayQty);
    }
}
