package com.renpho.erp.tms.application.transferorder.job.jd;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.apiproxy.jd.fop.JdResponse;
import com.renpho.erp.apiproxy.jd.fop.ProxyRoute;
import com.renpho.erp.apiproxy.jd.fop.api.FopInstockApi;
import com.renpho.erp.apiproxy.jd.fop.model.instock.QueryInStockPageData;
import com.renpho.erp.apiproxy.jd.fop.model.instock.QueryInStockPageRequest;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderService;
import com.renpho.erp.tms.application.transferorder.job.common.TransferOrderCommonService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;

import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.inbound.InboundRequestHistory;
import com.renpho.erp.tms.domain.inbound.InboundStatus;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transferorder.*;

import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.domain.transportrequest.dto.WarehouseData;

import com.renpho.erp.tms.infrastructure.persistence.transferorder.repository.TransferOrderItemRepository;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;

/**
 * TS-目的仓为JD-入库单签收任务.
 *
 * <AUTHOR>
 * @since 2025/8/27
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransferOrderInJdDeliveryService {

    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransferOrderCommonService transferOrderCommonService;
    private final TransferOrderService transferOrderService;
    private final PushTaskService pushTaskService;

    private final TransferOrderRepository transferOrderRepository;
    private final TransferOrderItemRepository transferOrderItemRepository;

    private final FopInstockApi fopInstockApi;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.JD;

//    @EventListener(ApplicationReadyEvent.class)
//    @Transactional(rollbackFor = Exception.class)
//    public void test() {
//        //testDelivery("TR2507310003");
//        //createInboundTask(List.of("TR2507310003"));
//    }

    /**
     * TS-目的仓为JD-入库单签收任务.
     */
    @Lock4j(name = "transfer:request:jd:inbound:delivery:basic")
    //@Transactional(rollbackFor = Exception.class)
    public void createInboundTask(List<String> trNoList) {
        try {
            log.info("TS-目的仓为JD的定时器任务开始");

            // 状态是部分签收、已发货
            List<TransferOrderStatus> statusList = List.of(TransferOrderStatus.SHIPPED, TransferOrderStatus.RECEIVING);
            List<TransferOrderData> tsDataList = transferOrderService.selectTsListByStatusList(statusList, warehouseType);

            // 外部参数
            tsDataList = transferOrderCommonService.fillExterWithTsStatus(statusList, tsDataList, trNoList);

            // 生成入库单推送任务
            if (tsDataList != null && !tsDataList.isEmpty()) {
                // 获取ts上下文信息
                Map<Integer, TransferOrder> tsMap = transferOrderService.getTsMapByIds(tsDataList.stream().map(TransferOrderData::getId).toList());

                for (TransferOrderData tsData : tsDataList) {
                    try {
                        TransferOrder ts = tsMap.get(tsData.getId());
                        if (ts == null) {
                            log.error("TS-目的仓为JD-入库单签收任务生成异常, ts上下文找不到, tsId={}", tsData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (warehouseType != ts.findWarehouseProviderType()) {
                            continue;
                        }

                        // 生成推送任务
                        PushTask trPushTask = pushTaskService.inbound(ts, PushTaskStatus.PENDING,0,null);

                        // 执行签收
                        pushTaskService.execute(() -> this.preHandlerDelivery(ts, trPushTask), trPushTask);
                    } catch (Exception e) {
                        log.error("TS-目的仓为JD-入库单签收任务异常, 入库单推送任务生成失败, tsId={}", tsData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TS-目的仓为JD-入库单签收任务", e);
        }
    }

    /**
     * 处理签收-前置
     */
    private Boolean preHandlerDelivery(TransferOrder ts, PushTask trPushTask){
        QueryInStockPageRequest queryDto = new QueryInStockPageRequest();
        queryDto.setPage(1);
        queryDto.setPageSize(1000);
        queryDto.setCustomerBillCode(ts.getTsNo());  // 京东特别，用tsNo

        WarehouseData warehouseData = transferOrderCommonService.getJDWarehouse(ts.getDestWarehouse().getId().id());
        if (warehouseData == null) {
            log.error("TS-目的仓为JD的定时器任务异常, 无法获取客户编码, tsId={}, destWarehouseId={}", ts.getId(), ts.getDestWarehouse().getId().id());
            return false;
        }
        ProxyRoute proxyRoute = new ProxyRoute();
        proxyRoute.setCustomerCode(warehouseData.getCustomerCode());
        queryDto.setCustomerCode(warehouseData.getCustomerCode());
        queryDto.setWarehouseCode(warehouseData.getWarehouseCode());

        log.info("TS-目的仓为JD-签收任务, 调用查询API, 参数={}", JSON.toJSONString(queryDto));
        R<JdResponse<List<QueryInStockPageData>>> result = fopInstockApi.queryInStockList(proxyRoute, queryDto);
        log.info("TS-目的仓为JD-签收任务, 调用查询API, 响应={}", JSON.toJSONString(result));
        Integer resultCode = 200;
        Object body = null;
        if (result != null && result.getData().getResponse() != null) {
            resultCode = result.getData().getResponse().getCode();
            if (result.getData().getResponse().getContent().isSuccess()) {
                body = result.getData().getResponse().getContent();

                // 记录日志
                InboundRequestHistory hisRecord = inboundRequestHistoryService.add(ts, WarehouseProviderType.JD, null, queryDto, resultCode, null, result);
                List<QueryInStockPageData> listInstock = result.getData().getResponse().getContent().getData();

                if(listInstock==null){
                    inboundRequestHistoryService.add(ts, WarehouseProviderType.JD, null, queryDto, resultCode, null, result);
                    throw new DingTalkWarning("API获取入库单失败，原因：%s".formatted("返回为空, tsId = "+ts.getId().id()));
                }

                // 签收处理
                SpringUtil.getBean(this.getClass()).handlerDelivery(listInstock, ts, trPushTask, hisRecord);
            } else {
                // 记录日志
                inboundRequestHistoryService.add(ts, WarehouseProviderType.JD, null, queryDto, resultCode, null, result);
                throw new DingTalkWarning("API获取入库单失败，原因：%s".formatted(result.getData().getResponse().getContent().getErrorMsg()));
            }
        } else {
            // 记录日志
            inboundRequestHistoryService.add(ts, WarehouseProviderType.JD, null, queryDto, resultCode, null, result);
            throw new DingTalkWarning("API获取入库单失败，原因：%s".formatted("返回为空, tsId = "+ts.getId().id()));
        }
        return true;
    }

    /**
     * 处理签收
     */
    @Transactional(rollbackFor = Exception.class)
    public void handlerDelivery(List<QueryInStockPageData> listInstock, TransferOrder ts, PushTask trPushTask, InboundRequestHistory hisRecord) {
        List<TransferOrderItem> itemList = ts.getItems();

        // 签收数据
        QueryInStockPageData vo = listInstock.get(0);

        // 已开始验收(50) = 部分签收， 已开始上架(60)/已完成(70) = 签收完成， 如果是部分签收，需要把tr单的状态从已派送改为部分签收
        switch (vo.getStatus()) {
            case 50:
                // 部分签收
                for(TransferOrderItem tsItem : itemList){
                    for(QueryInStockPageData.InstockDetailQueryVO detail : vo.getInstockDetailList()){
                        if(tsItem.getFnSku().equals(detail.getSku())){  // TODO 不确定是Fnsku还是sku
                            partSignIn(tsItem, detail, ts, vo, hisRecord, InboundStatus.WORKING);
                        }
                    }
                }
                transferOrderRepository.updateById(ts);
                transferOrderItemRepository.update(ts);
                break;
            case 60:
                // 已开始上架
                for(TransferOrderItem tsItem : itemList){
                    for(QueryInStockPageData.InstockDetailQueryVO detail : vo.getInstockDetailList()){
                        if(tsItem.getFnSku().equals(detail.getSku())){  // TODO 不确定是Fnsku还是sku
                            partSignIn(tsItem, detail, ts, vo, hisRecord, InboundStatus.WORKING);
                        }
                    }
                }
                transferOrderRepository.updateById(ts);
                transferOrderItemRepository.update(ts);
                break;
            case 70:
                // 签收完成
                for(TransferOrderItem tsItem : itemList){
                    for(QueryInStockPageData.InstockDetailQueryVO detail : vo.getInstockDetailList()){
                        if(tsItem.getFnSku().equals(detail.getSku())){  // TODO 不确定是Fnsku还是sku
                            partSignIn(tsItem, detail, ts, vo, hisRecord, InboundStatus.WORKING);

                            // 计算差异数量 = 签收数量 - 计划发货数量
                            Integer diffQuantity = detail.getRealQty().intValue() - tsItem.getQty();
                            tsItem.setReceivedDiscrepancy(diffQuantity);

                            // 上架差异数量
                            tsItem.setPutawayDiscrepancy(tsItem.getPutawayQty() - tsItem.getQty());
                        }
                    }
                }
                ts.setSyncApiStatus(SyncApiStatus.SUCCESS);
                ts.setStatus(TransferOrderStatus.RECEIVED);  // 没有分已签收跟已完成，直接已签收
                transferOrderRepository.updateById(ts);
                transferOrderItemRepository.update(ts);

                // 同步TR单状态，有差异数量已签收，没有差异数量已完成
                // transportRequestCommonService.syncTransportRequestStatus(diffQuantity, tr);

                // 维护 ts上架的生命周期
                pushTaskService.putaway(ts, PushTaskStatus.SUCCESS, 0, null);
                break;
            default:
                break;
        }
    }

    /**
     * 部分签收
     */
    private void partSignIn(TransferOrderItem tsItem, QueryInStockPageData.InstockDetailQueryVO detail, TransferOrder ts, QueryInStockPageData vo, InboundRequestHistory hisRecord, InboundStatus inboundStatus){
        tsItem.setReceivedQty(detail.getRealQty().intValue());
        tsItem.setPutawayQty(detail.getRealQty().intValue());

        // TODO 首次签收推送签收消息到FMS
        //transportRequestService.firstReceiveSendReceiveMsgToFms(tr.getToNo(), tr.getReceivedTime() == null);

        if(tsItem.getReceivedStartTime()==null){
            tsItem.setReceivedStartTime(LocalDateTime.now());
        }
        if(vo.getScanInboundTime()!=null && tsItem.getReceivedEndTime()==null){
            tsItem.setReceivedEndTime(vo.getScanInboundTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
            // 开始时间大于结束时间，需要纠正
            if(tsItem.getReceivedStartTime().getDayOfYear()>tsItem.getReceivedEndTime().getDayOfYear()){
                tsItem.setReceivedStartTime(tsItem.getReceivedEndTime());
            }
        }

        // 入库单状态签收中的，则TS单出运状态由“已发货”更新为“部分签收”
        if (TransferOrderStatus.SHIPPED == ts.getStatus()) {
            ts.setStatus(TransferOrderStatus.RECEIVING);
        }

        // 记录record
        Integer receiveQty = detail.getRealQty().intValue();
        LocalDateTime finishTime = null;
        LocalDateTime putAwayTime = null;
        if(InboundStatus.WORKING == inboundStatus && vo.getScanInboundTime()!=null){
            finishTime = vo.getScanInboundTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        } else if(InboundStatus.FINISH == inboundStatus && vo.getScanInboundTime()!=null){
            finishTime = vo.getScanInboundTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            putAwayTime = vo.getFinishTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        }

        // 记录records
        transferOrderCommonService.doRecord(warehouseType, receiveQty, receiveQty, finishTime, putAwayTime, tsItem, hisRecord, inboundStatus);
    }

}
