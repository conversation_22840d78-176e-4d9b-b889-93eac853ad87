package com.renpho.erp.tms.application.transportrequest;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.renpho.erp.apiproxy.amazon.model.ShopAccount;
import com.renpho.erp.apiproxy.amazon.model.fbaInbound.GetShipmentsV0Request;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.tms.application.discrepancy.DiscrepancyService;
import com.renpho.erp.tms.application.inbound.InboundAllocationProducer;
import com.renpho.erp.tms.application.inbound.InboundRecordQueryService;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transportorder.TransportOrderService;
import com.renpho.erp.tms.domain.inbound.*;
import com.renpho.erp.tms.domain.store.Store;
import com.renpho.erp.tms.domain.transportorder.TransportOrderStatusEnum;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.common.constant.CommonConstant;
import com.renpho.erp.tms.infrastructure.persistence.inbound.po.converter.InboundRecordConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderItemRepository;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.ShopAccountBuilder;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.inbound.AmazonInboundClient;
import com.renpho.erp.tms.infrastructure.remote.store.repository.StoreLookup;
import com.renpho.karma.dto.R;
import io.swagger.client.model.fbaInboundV0.*;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/8/6
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FBAInboundService {
    private final TransportRequestQueryService transportRequestQueryService;
    private final AmazonInboundClient amazonInboundClient;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final InboundRecordRepository inboundRecordRepository;
    private final InboundRecordQueryService inboundRecordQueryService;
    private final InboundRecordConverter inboundRecordConverter;
    private final InboundAllocationProducer inboundAllocationProducer;
    private final PushTaskService pushTaskService;
    private final TransportRequestOrderRepository transportRequestOrderRepository;
    private final TransportRequestOrderItemRepository transportRequestOrderItemRepository;
    private final TransportOrderService transportOrderService;
    private final TransportRequestService transportRequestService;
    private final StoreLookup storeLookup;
    private final DiscrepancyService discrepancyService;

    //FBA签收状态
    final List<ShipmentStatus> FBA_RECEIVED_STATUS = Arrays.asList(ShipmentStatus.DELIVERED, ShipmentStatus.CHECKED_IN,
            ShipmentStatus.RECEIVING, ShipmentStatus.CLOSED);
    //FBA上架状态
    final List<ShipmentStatus> FBA_ON_SHELVES_STATUS = Arrays.asList(ShipmentStatus.RECEIVING, ShipmentStatus.CLOSED);


    /**
     * 获取FBA平台仓入库单
     *
     * @param trNos TR单号
     * @param trIds TRIDs
     */
    public void pullShipmentsFromFBA(List<String> trNos, List<TransportRequestId> trIds, String syncApiStatus) {

        //获取所有状态为已派送或部分签收的FBA TR单
        List<TransportRequest> fbaTrs =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.FBA, trIds, trNos,
                        Arrays.asList(TransportOrderStatusEnum.OUT_FOR_DELIVERY,
                                TransportOrderStatusEnum.DELIVERED_PART), null, syncApiStatus);

        //按照Store进行分组
        Map<Store, List<TransportRequest>> shipmentsGroupByStore = CollectionUtil.emptyIfNull(fbaTrs).stream()
                .collect(Collectors.groupingBy(TransportRequest::getStore));
        for (Map.Entry<Store, List<TransportRequest>> entry : shipmentsGroupByStore.entrySet()) {
            List<TransportRequest> trs = entry.getValue();
            Store store = entry.getKey();
            StoreAuthorizationVo authorization = storeLookup.getAuthorization(store);
            //获取入库单详情
            for (TransportRequest tr : trs) {
                fetchAndProcessShipmentDetails(tr, authorization.getAuthorization());
            }
        }
    }


    /**
     * 组装获取入库单的参数
     *
     * @param shipmentId  shipmentId
     * @param authorization 授权信息
     * @return 获取入库单的参数
     */
    private GetShipmentsV0Request buildGetGetShipmentsV0Request(String shipmentId, StoreAuthorizationVo.Authorization authorization) {
        GetShipmentsV0Request req = new GetShipmentsV0Request();
        req.setMarketplaceId(authorization.getAmzMarketplaceId());
        req.setQueryType("SHIPMENT");
        req.setShipmentIdList(List.of(shipmentId));
        req.setShipmentStatusList(FBA_RECEIVED_STATUS.stream().map(ShipmentStatus::getValue).toList());
        return req;
    }


    /**
     * 获取FBA平台仓入库单详情
     *
     * @param tr            TR
     * @param authorization 授权
     */
    private void fetchAndProcessShipmentDetails(TransportRequest tr,
                                                StoreAuthorizationVo.Authorization authorization) {
        ShopAccount shopAccount = ShopAccountBuilder.buildAmzShopAccount(authorization);
        GetShipmentsV0Request req = buildGetGetShipmentsV0Request(tr.getShipmentId(), authorization);

        try {
            // 保存请求记录
            InboundRequestHistory history = inboundRequestHistoryService.add(tr, WarehouseProviderType.FBA, null, req);
            PushTask inboundPushTask = pushTaskService.inbound(tr, PushTaskStatus.PENDING, 0, null);
            // 调用详情接口，获取详情（首次调用）
            R<GetShipmentsResponse> ret = pushTaskService.execute(() -> {
                //第一次请求
                R<GetShipmentsResponse> r = amazonInboundClient.getShipments(shopAccount, req);
                //将异常抛出才能触发重试机制
                if (!r.isSuccess()) {
                    log.error("拉取【FBA-入库单列表】接口异常：, trNo={}", tr.getTrNo());
                    inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                    throw new RuntimeException("API获取入库单失败，原因：%s".formatted(r.getMessage()));
                }
                return r;
            }, inboundPushTask);

            inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null, ret.getData());

            GetShipmentsResult payload = ret.getData().getPayload();
            this.handleShipmentResult(shopAccount, payload.getShipmentData(), tr, inboundPushTask);
            // 分页处理 nextToken
            String nextToken = payload.getNextToken();
            while (StringUtil.isNotBlank(nextToken)) {
                GetShipmentsResult nextPayload = this.getShipmentsByNextToken(tr, shopAccount,
                        authorization.getAmzMarketplaceId(), nextToken, inboundPushTask);
                this.handleShipmentResult(shopAccount, nextPayload.getShipmentData(), tr, inboundPushTask);
                nextToken = nextPayload.getNextToken();
            }

        } catch (Exception e) {
            log.error("拉取【FBA-入库单列表】失败, trNo={}", tr.getTrNo(), e);
        }
    }

    /**
     * 通过下一个token获取入库单
     *
     * @param shopAccount     店铺信息
     * @param nextToken       下一个token
     * @param inboundPushTask 入库推送任务
     * @return 入库单列表
     */
    private GetShipmentsResult getShipmentsByNextToken(TransportRequest tr,
                                                       ShopAccount shopAccount,
                                                       String amzMarketplaceId,
                                                       String nextToken,
                                                       PushTask inboundPushTask) {
        GetShipmentsV0Request req = new GetShipmentsV0Request();
        req.setMarketplaceId(amzMarketplaceId);
        req.setQueryType("NEXT_TOKEN");
        req.setNextToken(nextToken);

        // 保存请求记录
        InboundRequestHistory history = inboundRequestHistoryService.add(tr, WarehouseProviderType.FBA, null, req);

        // 调用详情接口，获取详情（首次调用）
        R<GetShipmentsResponse> ret = pushTaskService.execute(() -> {
            R<GetShipmentsResponse> r = amazonInboundClient.getShipments(shopAccount, req);
            //将异常抛出才能触发重试机制
            if (!r.isSuccess()) {
                log.error("调用【FBA-分页查询入库单】接口异常：, trNo={}，nextToken：【{}】, 异常信息：{}", tr.getTrNo(), nextToken, r);
                inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                throw new RuntimeException("API获取入库单失败，原因：%s".formatted(r.getMessage()));
            }
            return r;
        }, inboundPushTask);

        inboundRequestHistoryService.update(history.getId(), Integer.parseInt(ret.getCode()), null, ret.getData());

        return ret.getData().getPayload();
    }


    /**
     * 根据Shipment获取ShipmentsItems
     *
     * @param shopAccount         店铺账号
     * @param inboundShipmentList shipmentList
     * @param tr                  tr
     * @param inboundPushTask     入库推送任务
     */
    private void handleShipmentResult(ShopAccount shopAccount,
                                     InboundShipmentList inboundShipmentList,
                                     TransportRequest tr,
                                     PushTask inboundPushTask) {

        if (CollectionUtils.isEmpty(inboundShipmentList)) {
            return;
        }

        for (InboundShipmentInfo shipmentData : inboundShipmentList) {
            ShipmentStatus shipmentStatus = shipmentData.getShipmentStatus();
            String shipmentId = shipmentData.getShipmentId();

            // 保存请求记录
            InboundRequestHistory history = inboundRequestHistoryService.add(tr, WarehouseProviderType.FBA, null, shipmentId);

            // 调用详情接口，获取详情（首次调用）
            R<GetShipmentItemsResponse> ret = pushTaskService.execute(() -> {
                R<GetShipmentItemsResponse> r = amazonInboundClient.getShipmentItemsByShipmentId(shopAccount, shipmentId);
                //将异常抛出才能触发重试机制
                if (!r.isSuccess()) {
                    log.error("获取【FBA-入库单Item】接口异常：, trNo={}, 异常信息：{}", tr.getTrNo(), r);
                    inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                    throw new RuntimeException("API获取入库单失败，原因：%s".formatted(r.getMessage()));
                }
                return r;
            }, inboundPushTask);

            // 保存请求记录
            inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null, ret.getData());

            GetShipmentItemsResult itemsResult = ret.getData().getPayload();
            LocalDateTime triggerTime = LocalDateTime.now();
            SpringUtil.getBean(this.getClass()).handleShipmentItems(itemsResult.getItemData(), shipmentStatus, tr, triggerTime, history.getId());

        }
    }

    /**
     * 处理 FBA 类型入库的 ShipmentItem 数据，包括签收与上架的记录生成及 TR 单状态同步。
     *
     * @param itemData         分页获取的 ShipmentItem 列表
     * @param shipmentStatus   当前处理的货件状态
     * @param tr               当前处理的 TR 单对象
     * @param triggerTime      当前操作的触发时间（如接口返回的操作时间）
     * @param requestHistoryId 请求历史记录 ID，用于标记入库记录归属
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleShipmentItems(InboundShipmentItemList itemData,
                                    ShipmentStatus shipmentStatus,
                                    TransportRequest tr,
                                    LocalDateTime triggerTime,
                                    InboundRequestHistoryId requestHistoryId) {
        if (CollectionUtils.isEmpty(itemData)) {
            log.warn("分页的ShipmentItems为空");
            return;
        }

        // 查询上一次记录，用于差异计算
        InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tr).orElse(null);

        for (InboundShipmentItem item : itemData) {
            processSingleItem(item, shipmentStatus, tr, triggerTime, requestHistoryId, lastRecord);
        }
    }

    /**
     * 处理单个 ShipmentItem 的签收与上架逻辑，并记录入库信息。
     */
    private void processSingleItem(InboundShipmentItem item,
                                   ShipmentStatus shipmentStatus,
                                   TransportRequest tr,
                                   LocalDateTime triggerTime,
                                   InboundRequestHistoryId requestHistoryId,
                                   InboundRecord lastRecord) {

        // 构建新的入库记录对象
        InboundRecord record = inboundRecordConverter.toDomain(tr);
        record.setWarehouseType(WarehouseProviderType.FBA);
        record.setRequestHistoryId(requestHistoryId);

        Integer receivedQty = tr.getItem().getQuantity(); // 实际签收数量默认为发货数量
        Integer putawayQty = Optional.ofNullable(item.getQuantityReceived()).orElse(0); // 上架数量

        boolean haveNewReceived = false, haveNewPutaway = false;
        boolean firstReceived = tr.checkTrIsFirstReceived();
        // 处理签收逻辑（如果当前状态属于签收状态）
        if (FBA_RECEIVED_STATUS.contains(shipmentStatus)) {
            haveNewReceived = handleReceived(record, tr, receivedQty, triggerTime, lastRecord);
        }

        // 处理上架逻辑（如果当前状态属于上架状态）
        if (FBA_ON_SHELVES_STATUS.contains(shipmentStatus)) {
            haveNewPutaway = handlePutaway(record, tr, putawayQty, triggerTime, lastRecord);
        }

        // 更新 TR Item 的签收和上架数量
        updateItemQuantities(tr, receivedQty, putawayQty);

        // 若货件状态为 CLOSED，说明已完成签收与上架
        if (ShipmentStatus.CLOSED.equals(shipmentStatus)) {
            record.setPutawayStatus(InboundStatus.FINISH);
            tr.setShipStatus(TransportOrderStatusEnum.DELIVERED);
        }

        tr.setSyncApiStatus(SyncApiStatus.SUCCESS);
        // 如果本次存在新增的签收或上架记录，则落库并更新状态
        if (haveNewReceived || haveNewPutaway) {
            persistChanges(tr, record, shipmentStatus, firstReceived);
        }
    }

    /**
     * 构建签收信息并更新 TR 单签收状态。
     */
    private boolean handleReceived(InboundRecord record,
                                   TransportRequest tr,
                                   Integer receivedQty,
                                   LocalDateTime triggerTime,
                                   InboundRecord lastRecord) {
        record.setStatus(InboundStatus.FINISH);
        record.setPutawayStatus(receivedQty > 0 ? InboundStatus.WORKING : InboundStatus.PENDING);

        boolean updated = record.buildReceivedInfo(receivedQty, lastRecord);
        record.setReceivedTime(triggerTime);

        // 更新 TR 单签收状态
        tr.setShipStatus(TransportOrderStatusEnum.DELIVERED_PART);

        // 如果是首次签收，记录时间
        if (tr.checkTrIsFirstReceived()) {
            tr.setReceivedTime(triggerTime);
        }

        return updated;
    }

    /**
     * 构建上架信息并更新 TR 单上架状态。
     */
    private boolean handlePutaway(InboundRecord record,
                                  TransportRequest tr,
                                  Integer putawayQty,
                                  LocalDateTime triggerTime,
                                  InboundRecord lastRecord) {
        boolean updated = record.buildPutawayInfo(putawayQty, lastRecord);
        record.setPutawayTime(triggerTime);
        record.setPutawayStatus(InboundStatus.WORKING);

        // 记录上架完成时间
        tr.setReceivedEndTime(triggerTime);
        return updated;
    }

    /**
     * 更新 TR Item 的签收和上架数量。
     */
    private void updateItemQuantities(TransportRequest tr, Integer receivedQty, Integer putawayQty) {
        tr.getItem().setReceivedQuantity(receivedQty);
        tr.getItem().setShelvedQuantity(putawayQty);
    }

    /**
     * 持久化 TR 状态和入库记录，并根据业务规则判断是否进入“已完成”状态。
     */
    private void persistChanges(TransportRequest tr,
                                InboundRecord record,
                                ShipmentStatus shipmentStatus,
                                boolean isFirstReceive) {
        // 更新 TR 与 TR Item
        transportRequestOrderRepository.updateById(tr);
        transportRequestOrderItemRepository.update(tr);

        // 同步 TO 的状态（如有必要）
        transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.DELIVERED);

        // 如果货件已关闭，尝试判断差异并更新状态为“已完成”
        if (ShipmentStatus.CLOSED.equals(shipmentStatus)) {
            boolean haveDiscrepancy = tr.getItem().calculateTheDifferenceValue(
                    tr.getItem().getReceivedQuantity(),
                    tr.getItem().getShelvedQuantity()
            );
            transportRequestOrderItemRepository.update(tr);

            if (!haveDiscrepancy) {
                tr.setShipStatus(TransportOrderStatusEnum.COMPLETED);
                transportRequestOrderRepository.updateById(tr);
                transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.COMPLETED);
            }else {
                //有签收差异，调用IMS，创建差异单
                discrepancyService.handleTrDiscrepancy(tr);
            }
        }

        // 保存签收/上架记录
        record = inboundRecordRepository.add(record);
        //入库记录的运费与税费分摊逻辑
        inboundAllocationProducer.send(record);
        // 首次签收后推送消息给 FMS
        transportRequestService.firstReceiveSendReceiveMsgToFms(tr.getToNo(), isFirstReceive);
    }
}
