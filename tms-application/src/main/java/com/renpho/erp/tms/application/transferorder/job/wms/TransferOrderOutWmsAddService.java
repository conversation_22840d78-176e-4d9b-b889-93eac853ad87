package com.renpho.erp.tms.application.transferorder.job.wms;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.ftm.client.request.FileRequest;
import com.renpho.erp.ftm.client.response.FileDetailResponse;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.orderfile.OrderFileService;
import com.renpho.erp.tms.application.transferorder.job.common.TransferOrderCommonService;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.orderfile.BusinessTypeEnum;
import com.renpho.erp.tms.domain.orderfile.OrderFile;
import com.renpho.erp.tms.domain.transferorder.*;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.repository.TransferOrderItemRepository;
import com.renpho.erp.tms.infrastructure.remote.file.RemoteFileFeign;
import com.renpho.erp.tms.infrastructure.remote.wms.dto.outbound.*;
import com.renpho.erp.tms.infrastructure.remote.wms.feign.WmsOutboundFeign;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TS-目的仓为WMS-创建出库单.
 *
 * <AUTHOR>
 * @since 2025/8/26
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransferOrderOutWmsAddService {

    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransferOrderCommonService transferOrderCommonService;
    private final TransferOrderItemRepository transferOrderItemRepository;
    private final OrderFileService orderFileService;

    private final TransferOrderRepository transferOrderRepository;
    private final WmsOutboundFeign wmsOutboundFeign;
    private final RemoteFileFeign remoteFileFeign;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.WMS;

//    @EventListener(ApplicationReadyEvent.class)
//    public void test() {
//    }

    /**
     * TS-目的仓为WMS-创建出库单.
     *
     * @param ts         ts上下文
     */
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = "transfer:request:wms:outbound:add:basic")
    public Boolean createOutboundOrder(TransferOrder ts) {
        // 目的仓限制
        if (warehouseType != ts.findWarehouseProviderType()) {
            throw new BusinessException("TRANSFER_ORDER_OUT_NOT_WMS");
        }

        String token = transferOrderCommonService.getWmsAuthToken(ts.getDestWarehouse().getId().id());
        String fromIn = "Y"; // 内部请求,避开erp认证拦截器

        OutboundMainDto outboundMainDto = new OutboundMainDto();

        OutboundOrderDto outboundOrderDto = new OutboundOrderDto();
        outboundOrderDto.setDataSource(String.valueOf(1));
        outboundOrderDto.setToNo(ts.getTsNo());
        outboundOrderDto.setTrNo(ts.getTsNo());
        outboundOrderDto.setType(0);
        outboundOrderDto.setWarehouseId(ts.getDestWarehouse().getId().id());
        outboundOrderDto.setSalesPic(String.valueOf(ts.getSalesStaffId().id()));
        outboundOrderDto.setLogisticsPic(String.valueOf(ts.getShippingStaffId().id()));
        outboundOrderDto.setExpectedDeliveryTime(ts.getEstimatedDeliveryTime().toLocalDate());
        outboundMainDto.setOrder(outboundOrderDto);

        OutboundOrderShipInfoDto outboundOrderShipInfoDto = new OutboundOrderShipInfoDto();
        outboundOrderShipInfoDto.setShipmentId(ts.getShipmentId());
        outboundOrderShipInfoDto.setShipmentType(4);  // 其它
        outboundOrderShipInfoDto.setUrgent(false);
        outboundOrderShipInfoDto.setPallet(ts.getIsPalletized());  // TS的是否打托
        outboundMainDto.setOrderShipInfo(outboundOrderShipInfoDto);

        // 外箱链接
        List<OrderFile> allTypeList = orderFileService.allTypeList(ts.getId().id(), BusinessTypeEnum.TS);
        if(allTypeList != null && !allTypeList.isEmpty()){
            FileRequest fileRequest = new FileRequest();
            List<String> ids = allTypeList.stream()
                    .map(item -> String.valueOf(item.getId()))
                    .collect(Collectors.toList());
            fileRequest.setFileIds(ids);
            fileRequest.setContentDisposition("inline");
            log.info("获取外箱链接文件信息,请求参数:{}", JSON.toJSONString(fileRequest));
            R<List<FileDetailResponse>> fileRes = remoteFileFeign.getFileInfoList(fileRequest);
            log.info("获取外箱链接文件信息,响应参数:{}", JSON.toJSONString(fileRes));
            if(fileRes.isSuccess()){
                List<FileDetailResponse> fileDetailResponses = fileRes.getData();
                if(!fileDetailResponses.isEmpty()){
                    outboundOrderDto.setBoxLabelUrlList(fileDetailResponses.stream().map(FileDetailResponse::getUrl).collect(Collectors.toList()));
                }
            }else {
                throw new BusinessException("FMS_FILE_INFO_ERROR");  // fms接口异常
            }
        }

        List<OutboundOrderItemDto> orderItemList = new ArrayList<>();
        List<TransferOrderItem> tsItems = ts.getItems();
        for(TransferOrderItem tsItem : tsItems){
            OutboundOrderItemDto item = new OutboundOrderItemDto();

            if(tsItem.getIsBorrowed()){
                item.setIsBorrowed(1);
                item.setPsku(tsItem.getBorrowedPsku());
                item.setBarcode(tsItem.getBorrowedFnsku());
                item.setOwnerId(tsItem.getBorrowedOwnerId().id());
                item.setOutboundBarcode(tsItem.getBorrowedFnsku());

                // 借货才需要传的3个字段
                item.setBorrowedPsku(tsItem.getPsku());
                item.setBorrowedBarcode(tsItem.getFnSku());
                item.setBorrowedOwnerId(ts.getOwnerId().id());
            } else{
                item.setIsBorrowed(0);
                item.setPsku(ts.getPsku());
                item.setBarcode(tsItem.getFnSku());
                item.setOwnerId(ts.getOwnerId().id());
                item.setOutboundBarcode(tsItem.getFnSku());
            }

            if(tsItem.getIsRelabel()){
                item.setIsRelabel(1);

                FileRequest fileRequest = new FileRequest();
                fileRequest.setFileIds(tsItem.getNewProductLabelFileIds());
                fileRequest.setContentDisposition("inline");
                log.info("获取产品标签链接文件信息,请求参数:{}", JSON.toJSONString(fileRequest));
                R<FileDetailResponse> fileRes = remoteFileFeign.getFileInfo(fileRequest);
                log.info("获取产品标签链接文件信息,响应参数:{}", JSON.toJSONString(fileRes));
                if(fileRes.isSuccess()){
                    FileDetailResponse fileDetailResponse = fileRes.getData();
                    if(fileDetailResponse!=null){
                        item.setProductLabelUrl(fileDetailResponse.getUrl());
                    }
                }else {
                    throw new BusinessException("FMS_FILE_INFO_ERROR");  // fms接口异常
                }
            } else{
                item.setIsRelabel(0);
            }
            item.setAttributes(1);  // 默认良品
            item.setPlanQty(tsItem.getQty());
            orderItemList.add(item);
        }
        outboundMainDto.setOrderItemList(orderItemList);

        log.info("TS-目的仓为WMS-执行出库单, 参数={}", JSON.toJSONString(outboundMainDto));
        R<OutboundCreateMainVo> result = wmsOutboundFeign.addOutboundOrder(fromIn, token, outboundMainDto);
        log.info("TS-目的仓为WMS-执行出库单, 响应={}", JSON.toJSONString(result));
        Integer resultCode = 200;
        Object bodyData = null;
        if (result != null) {
            bodyData = result.getData();
            if (result.isSuccess()) {
                // 记录请求历史
                inboundRequestHistoryService.add(ts, WarehouseProviderType.WMS, null, outboundMainDto, resultCode, null, result);

                // 创建成功，保存
                OutboundCreateMainVo itemResult = result.getData();
                for(TransferOrderItem tsItem : tsItems){
                    boolean isFind = false;
                    for(OutboundCreateItemVo orderItemDto : itemResult.getItemList()){
                        if(orderItemDto.getPsku().equals(tsItem.getPsku())){
                            tsItem.setOutboundNo(orderItemDto.getTrNo());
                            isFind = true;
                            break;
                        }
                    }
                    if(!isFind){
                        throw new BusinessException("WMS_OUTBOUND_ITEM_NOT_FOUND",tsItem.getPsku());  // 明细没有对应调拨单信息
                    }
                }
                ts.setSyncApiStatus(SyncApiStatus.SUCCESS);
                transferOrderRepository.updateById(ts);
                transferOrderItemRepository.update(ts);
            } else {
                // 记录请求历史
                inboundRequestHistoryService.add(ts, WarehouseProviderType.WMS, null, outboundMainDto,200, null, result);
                throw new DingTalkWarning("API创建出库单失败，原因：%s".formatted(result.getMessage()));
            }
        }
        return true;
    }

}
