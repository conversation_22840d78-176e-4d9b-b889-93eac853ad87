package com.renpho.erp.tms.application.transportrequest;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.renpho.erp.apiproxy.amazon.model.ShopAccount;
import com.renpho.erp.apiproxy.amazon.model.awd.GetInboundShipmentV20240509Request;
import com.renpho.erp.apiproxy.tiktok.model.TikTokResponse;
import com.renpho.erp.apiproxy.tiktok.model.fulfilledByTiktok.GetInboundOrderData;
import com.renpho.erp.apiproxy.tiktok.model.fulfilledByTiktok.GetInboundOrderRequest;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.GetInboundShipmentItemsRequest;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.GetInboundShipmentItemsResponse;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.GetShipmentsRequest;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.tms.application.discrepancy.DiscrepancyService;
import com.renpho.erp.tms.application.inbound.InboundAllocationProducer;
import com.renpho.erp.tms.application.inbound.InboundRecordQueryService;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transportorder.TransportOrderService;
import com.renpho.erp.tms.domain.inbound.*;
import com.renpho.erp.tms.domain.store.Store;
import com.renpho.erp.tms.domain.transportorder.TransportOrderStatusEnum;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.common.constant.CommonConstant;
import com.renpho.erp.tms.infrastructure.persistence.inbound.po.converter.InboundRecordConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderItemRepository;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.ShopAccountBuilder;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.inbound.AmazonInboundClient;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.inbound.TikTokInboundClient;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.inbound.WalmartInboundClient;
import com.renpho.erp.tms.infrastructure.remote.store.repository.StoreLookup;
import com.renpho.erp.tms.infrastructure.util.DateUtils;
import com.renpho.karma.dto.R;
import io.swagger.client.model.awd.*;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 平台仓入库单服务
 *
 * <AUTHOR>
 * @since 2025/7/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlatformWarehouseShipmentService {
    private final TransportRequestQueryService transportRequestQueryService;
    private final AmazonInboundClient amazonInboundClient;
    private final TikTokInboundClient tikTokInboundClient;
    private final WalmartInboundClient walmartInboundClient;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final InboundRecordRepository inboundRecordRepository;
    private final InboundRecordQueryService inboundRecordQueryService;
    private final InboundRecordConverter inboundRecordConverter;
    private final InboundAllocationProducer inboundAllocationProducer;
    private final PushTaskService pushTaskService;
    private final TransportRequestOrderRepository transportRequestOrderRepository;
    private final TransportRequestOrderItemRepository transportRequestOrderItemRepository;
    private final TransportOrderService transportOrderService;
    private final TransportRequestService transportRequestService;
    private final StoreLookup storeLookup;
    private final DiscrepancyService discrepancyService;

    //AWD签收状态
    final List<InboundShipmentStatus> AWD_RECEIVED_STATUS = Arrays.asList(InboundShipmentStatus.DELIVERED,
            InboundShipmentStatus.RECEIVING, InboundShipmentStatus.CLOSED);
    //AWD上架状态
    final List<InboundShipmentStatus> AWD_ON_SHELVES_STATUS = Arrays.asList(InboundShipmentStatus.RECEIVING,
            InboundShipmentStatus.CLOSED);

    /**
     * 获取AWD平台仓入库单
     *
     * @param trNos TR单号
     */
    public void pullShipmentsFromAWD(List<String> trNos, List<TransportRequestId> trIds, String syncApiStatus) {
        //获取所有状态为已派送或部分签收的AWD TR单
        List<TransportRequest> awdTrs =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.AWD, trIds, trNos,
                        Arrays.asList(TransportOrderStatusEnum.OUT_FOR_DELIVERY,
                                TransportOrderStatusEnum.DELIVERED_PART), null, syncApiStatus);

        //按照Store进行分组
        Map<Store, List<TransportRequest>> shipmentsGroupByStore = CollectionUtil.emptyIfNull(awdTrs).stream()
                .collect(Collectors.groupingBy(TransportRequest::getStore));
        for (Map.Entry<Store, List<TransportRequest>> entry : shipmentsGroupByStore.entrySet()) {
            List<TransportRequest> trs = entry.getValue();
            Store store = entry.getKey();
            StoreAuthorizationVo authorization = storeLookup.getAuthorization(store);

            //获取入库单详情
            ShopAccount shopAccount = ShopAccountBuilder.buildAmzShopAccount(authorization.getAuthorization());
            GetInboundShipmentV20240509Request request = new GetInboundShipmentV20240509Request();
            request.setSkuQuantities("SHOW");

            for (TransportRequest tr : trs) {

                try {
                    // 保存请求记录
                    InboundRequestHistory history = inboundRequestHistoryService.add(tr, WarehouseProviderType.AWD, null, tr.getShipmentId());
                    PushTask inboundPushTask = pushTaskService.inbound(tr, PushTaskStatus.PENDING, 0, null);

                    LocalDateTime triggerTime = LocalDateTime.now();
                    R<InboundShipment> ret = pushTaskService.execute(() -> {
                        R<InboundShipment> r = amazonInboundClient.getInboundShipment(shopAccount, tr.getShipmentId(), request);
                        //将异常抛出才能触发重试机制
                        if (!r.isSuccess()) {
                            log.error("调用【AWD-入库单列表】接口异常：, trNo={}，异常信息：{}", tr.getTrNo(), r);
                            inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                            throw new RuntimeException("API获取入库单失败，原因：%s".formatted(r.getMessage()));
                        }
                        return r;
                    }, inboundPushTask);

                    inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null, ret.getData());

                    SpringUtil.getBean(this.getClass()).handleInboundData(tr, ret.getData(), history.getId(), triggerTime);
                } catch (Exception e) {
                    log.error("拉取【AWD-入库单】失败, trNo={}", tr.getTrNo(), e);
                }
            }
        }
    }

    /**
     * 处理AWD入库单入库 数据
     *
     * @param tr              tr单
     * @param inboundShipment 入库单详情
     * @param historyId       请求历史ID
     * @param triggerTime     触发时间
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleInboundData(TransportRequest tr, InboundShipment inboundShipment,
                                  InboundRequestHistoryId historyId, LocalDateTime triggerTime) {

        //上次入库记录
        InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tr).orElse(null);

        List<DistributionPackageQuantity> shipmentContainerQuantities = inboundShipment.getShipmentContainerQuantities();
        int sumQty = 0;

        for (DistributionPackageQuantity shipmentContainerQuantity : shipmentContainerQuantities) {
            Optional<List<ProductQuantity>> productQuantities = Optional.ofNullable(shipmentContainerQuantity)
                    .map(DistributionPackageQuantity::getDistributionPackage)
                    .map(DistributionPackage::getContents)
                    .map(DistributionPackageContents::getProducts);

            //获取配送包裹中产品数量
            List<ProductQuantity> productQuantityList = productQuantities.orElse(new ArrayList<>());
            sumQty += productQuantityList.stream().map(ProductQuantity::getQuantity).mapToInt(Integer::intValue).sum();
        }


        InboundRecord record = inboundRecordConverter.toDomain(tr);
        record.setWarehouseType(WarehouseProviderType.AWD);
        record.setRequestHistoryId(historyId);
        boolean haveNewReceived = false, haveNewPutaway = false;

        boolean isFirstReceived = tr.checkTrIsFirstReceived();
        InboundShipmentStatus shipmentStatus = inboundShipment.getShipmentStatus();
        if (AWD_RECEIVED_STATUS.contains(shipmentStatus)) {
            //签收
            int receivedQty = tr.getItem().getQuantity();
            haveNewReceived = record.buildReceivedInfo(receivedQty, lastRecord);
            record.setStatus(InboundStatus.FINISH);
            record.setPutawayStatus(InboundStatus.PENDING);
            record.setReceivedTime(triggerTime);
            tr.setShipStatus(TransportOrderStatusEnum.DELIVERED_PART);
            if (isFirstReceived) {
                //设置TR单的开始签收时间
                tr.setReceivedTime(triggerTime);
            }
            //记录tr单的实际签收数量
            tr.getItem().setReceivedQuantity(receivedQty);
        }

        if (AWD_ON_SHELVES_STATUS.contains(shipmentStatus)) {
            //上架
            List<InventoryQuantity> receivedQuantity = inboundShipment.getReceivedQuantity();
            BigDecimal qty = receivedQuantity.stream().map(InventoryQuantity::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            int putAwayQty = qty.intValue() * sumQty;
            haveNewPutaway = record.buildPutawayInfo(putAwayQty, lastRecord);
            record.setPutawayTime(triggerTime);
            record.setPutawayStatus(InboundStatus.WORKING);
            //记录tr单的实际上架数量
            tr.getItem().setShelvedQuantity(putAwayQty);
        }


        if (InboundShipmentStatus.CLOSED.equals(shipmentStatus)) {
            //更新TR单出运状态更新“已签收”
            tr.setShipStatus(TransportOrderStatusEnum.DELIVERED);
            record.setPutawayStatus(InboundStatus.FINISH);
        }

        tr.setSyncApiStatus(SyncApiStatus.SUCCESS);
        //存在增量数据
        if (haveNewReceived || haveNewPutaway) {
            //更新TR单
            transportRequestOrderRepository.updateById(tr);
            transportRequestOrderItemRepository.update(tr);
            // 判断此TR单对应的TO单下所有TR是否全部都是已签收状态，如果是，则将TO状态改为已签收
            transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.DELIVERED);
            if (InboundShipmentStatus.CLOSED.equals(shipmentStatus)) {
                //签收完成后，计算签收、上架差异数量
                boolean haveDiscrepancy = tr.getItem().calculateTheDifferenceValue(tr.getItem().getReceivedQuantity(), tr.getItem().getShelvedQuantity());
                transportRequestOrderItemRepository.update(tr);
                //差异值处理完后，若无差异，更新TR单出运状态更新“已完成"
                if (!haveDiscrepancy) {
                    tr.setShipStatus(TransportOrderStatusEnum.COMPLETED);
                    //更新TR单
                    transportRequestOrderRepository.updateById(tr);
                    transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.COMPLETED);
                } else {
                    //有签收差异，调用IMS，创建差异单
                    discrepancyService.handleTrDiscrepancy(tr);
                }
            }

            //保存签收、上架数据
            record = inboundRecordRepository.add(record);
            inboundAllocationProducer.send(record);
            //首次签收推送签收消息到FMS
            transportRequestService.firstReceiveSendReceiveMsgToFms(tr.getToNo(), isFirstReceived);
        }

    }

    public void pullShipmentsFromFBT(List<String> trNos, List<TransportRequestId> trIds, String syncApiStatus) {
        //获取所有状态为已派送或部分签收的FBT TR单
        List<TransportRequest> fbtTrs =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.FBT, trIds, trNos,
                        Arrays.asList(TransportOrderStatusEnum.OUT_FOR_DELIVERY,
                                TransportOrderStatusEnum.DELIVERED_PART), null, syncApiStatus);


        //按照Store进行分组
        Map<Store, List<TransportRequest>> shipmentsGroupByStore = CollectionUtil.emptyIfNull(fbtTrs).stream()
                .collect(Collectors.groupingBy(TransportRequest::getStore));

        for (Map.Entry<Store, List<TransportRequest>> entry : shipmentsGroupByStore.entrySet()) {
            List<TransportRequest> trs = entry.getValue();
            Store store = entry.getKey();
            StoreAuthorizationVo authorization = storeLookup.getAuthorization(store);

            com.renpho.erp.apiproxy.tiktok.model.ShopAccount shopAccount = ShopAccountBuilder.buildTikTokShopAccount(authorization.getAuthorization());

            for (TransportRequest tr : trs) {
                GetInboundOrderRequest req = new GetInboundOrderRequest();
                req.setIds(tr.getShipmentId().replace("IBR", ""));

                // 保存请求记录
                InboundRequestHistory history = inboundRequestHistoryService.add(tr, WarehouseProviderType.FBT, null, req);
                try {
                    PushTask inboundPushTask = pushTaskService.inbound(tr, PushTaskStatus.PENDING, 0, null);
                    R<TikTokResponse<GetInboundOrderData>> ret = pushTaskService.execute(() -> {
                        R<TikTokResponse<GetInboundOrderData>> r = tikTokInboundClient.getInboundOrders(shopAccount, req);
                        //将异常抛出才能触发重试机制
                        if (!r.isSuccess()) {
                            log.error("获取【FBT-入库单列表】接口异常：, trNo={}, 异常信息：{}", tr.getTrNo(), r);
                            inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                            throw new RuntimeException("API获取入库单失败，原因：%s".formatted(r.getMessage()));
                        }
                        return r;
                    }, inboundPushTask);

                    inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null, ret.getData());


                    TikTokResponse<GetInboundOrderData> data = ret.getData();
                    SpringUtil.getBean(this.getClass()).handleTikTokInboundOrder(data, tr, history);

                } catch (Exception e) {
                    log.error("拉取【FBT入库单】失败, trNo={}", tr.getTrNo(), e);
                }
            }
        }


    }

    /**
     * 处理TikTok入库单
     *
     * @param data    tiktok响应数据
     * @param tr      TR单
     * @param history 请求记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleTikTokInboundOrder(TikTokResponse<GetInboundOrderData> data, TransportRequest tr,
                                         InboundRequestHistory history) {

        data.getData().getInbound_orders().forEach(inboundOrder -> {

            String status = inboundOrder.getOrder_operation_logs().stream()
                    .sorted(Comparator.comparingLong(GetInboundOrderData.OrderOperationLog::getOperate_time).reversed()) // 最新状态
                    .map(GetInboundOrderData.OrderOperationLog::getOrder_status)
                    .findFirst()
                    .orElse(null);

            InboundRecord record = inboundRecordConverter.toDomain(tr);
            record.setWarehouseType(WarehouseProviderType.FBT);
            record.setRequestHistoryId(history.getId());


            // 1. 签收处理
            if (Set.of("DELIVERED", "RECEIVING", "PARTIALLY_RECEIVED", "RECEIVED").contains(status)) {
                List<GetInboundOrderData.PlannedGoods> plannedGoods = inboundOrder.getPlanned_goods();
                long arriveTimeSeconds = inboundOrder.getActual_arrive_time();
                LocalDateTime receiveTime = Instant.ofEpochMilli(arriveTimeSeconds)
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();

                for (GetInboundOrderData.PlannedGoods goods : plannedGoods) {
                    boolean haveNewReceived, haveNewPutaway = false;
                    InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tr).orElse(null);

                    String goodsId = goods.getId();

                    record.setStatus(InboundStatus.FINISH);
                    record.setPutawayStatus(InboundStatus.PENDING);
                    haveNewReceived = record.buildReceivedInfo(tr.getItem().getQuantity(), lastRecord);
                    record.setReceivedTime(receiveTime);
                    boolean isFirstReceived = tr.checkTrIsFirstReceived();
                    if (isFirstReceived) {
                        //设置TR单的开始签收时间
                        tr.setReceivedTime(receiveTime);
                    }
                    tr.setShipStatus(TransportOrderStatusEnum.DELIVERED_PART);
                    //记录tr单的实际签收数量
                    tr.getItem().setReceivedQuantity(tr.getItem().getQuantity());

                    // 2. 上架处理（received_batches）
                    if (Set.of("PARTIALLY_RECEIVED", "RECEIVED").contains(status)) {
                        List<GetInboundOrderData.ReceivedBatch> batches = inboundOrder.getReceived_batches();
                        LocalDateTime shelvingTime = LocalDateTime.now(); // 当前时间为上架时间

                        List<GetInboundOrderData.ReceivedBatch> receivedBatchList = batches.stream()
                                .filter(batch -> batch.getGoods_id().equals(goodsId))
                                .toList();

                        //已签收的良品数量
                        int normalSum = 0;
                        //已签收的不良品数量
                        int defectiveSum = 0;
                        //累计的已签收数量
                        int totalQtySum = 0;

                        for (GetInboundOrderData.ReceivedBatch batch : receivedBatchList) {
                            normalSum += batch.normal_quantity;
                            defectiveSum += batch.defective_quantity;
                            totalQtySum += batch.total_quantity;
                        }

                        haveNewPutaway = record.buildPutawayInfo(totalQtySum, normalSum, defectiveSum, lastRecord);
                        record.setPutawayTime(shelvingTime);
                        record.setPutawayStatus(InboundStatus.WORKING);
                        //记录tr单的实际上架数量
                        tr.getItem().setShelvedQuantity(totalQtySum);

                    }
                    if ("RECEIVED".equals(status)) {
                        record.setPutawayStatus(InboundStatus.FINISH);
                        //更新TR单出运状态更新“已签收”
                        tr.setShipStatus(TransportOrderStatusEnum.DELIVERED);
                        tr.setReceivedEndTime(record.getReceivedTime());
                    }
                    tr.setSyncApiStatus(SyncApiStatus.SUCCESS);
                    //存在增量数据
                    if (haveNewReceived || haveNewPutaway) {
                        //更新TR单
                        transportRequestOrderRepository.updateById(tr);
                        transportRequestOrderItemRepository.update(tr);
                        // 判断此TR单对应的TO单下所有TR是否全部都是已签收状态，如果是，则将TO状态改为已签收
                        transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.DELIVERED);

                        if ("RECEIVED".equals(status)) {
                            //签收完成后，计算签收、上架差异数量
                            boolean haveDiscrepancy = tr.getItem().calculateTheDifferenceValue(tr.getItem().getReceivedQuantity(), tr.getItem().getShelvedQuantity());
                            transportRequestOrderItemRepository.update(tr);
                            //差异值处理完后，若无差异，更新TR单出运状态更新“已完成"
                            if (!haveDiscrepancy) {
                                tr.setShipStatus(TransportOrderStatusEnum.COMPLETED);
                                //更新TR单
                                transportRequestOrderRepository.updateById(tr);
                                transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.COMPLETED);
                            } else {
                                //有签收差异，调用IMS，创建差异单
                                discrepancyService.handleTrDiscrepancy(tr);
                            }
                        }

                        //保存签收、上架数据
                        record = inboundRecordRepository.add(record);
                        inboundAllocationProducer.send(record);

                        //首次签收推送签收消息到FMS
                        transportRequestService.firstReceiveSendReceiveMsgToFms(tr.getToNo(), isFirstReceived);
                    }
                }
            }
        });
    }

    /**
     * 查询状态为已派送或部分签收的Walmart TR单，获取Walmart的出运单信息
     *
     * @param trNos TR单号
     * @param trIds TR单ID
     */
    public void pullShipmentsFromWalmart(List<String> trNos, List<TransportRequestId> trIds, String syncApiStatus) {
        //获取所有状态为已派送或部分签收的WFS TR单
        List<TransportRequest> wfsTrs =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.WFS, trIds, trNos,
                        Arrays.asList(TransportOrderStatusEnum.OUT_FOR_DELIVERY,
                                TransportOrderStatusEnum.DELIVERED_PART), null, syncApiStatus);

        //将wfsTrs过滤渠道=walmart，按照Store进行分组
        Map<Store, List<TransportRequest>> shipmentsGroupByStore = CollectionUtil.emptyIfNull(wfsTrs).stream()
                .collect(Collectors.groupingBy(TransportRequest::getStore));
        for (Map.Entry<Store, List<TransportRequest>> entry : shipmentsGroupByStore.entrySet()) {
            List<TransportRequest> trs = entry.getValue();
            Store store = entry.getKey();
            StoreAuthorizationVo authorization = storeLookup.getAuthorization(store);

            com.renpho.erp.apiproxy.walmart.model.ShopAccount shopAccount = ShopAccountBuilder.buildWalmartShopAccount(authorization);

            for (TransportRequest tr : trs) {
                GetShipmentsRequest req = new GetShipmentsRequest();
                req.setShipmentId(tr.getShipmentId());

                // 保存请求记录
                InboundRequestHistory history = inboundRequestHistoryService.add(tr, WarehouseProviderType.WFS, null, req);

                try {
                    PushTask inboundPushTask = pushTaskService.inbound(tr, PushTaskStatus.PENDING, 0, null);
                    R<com.renpho.erp.apiproxy.walmart.model.fulfillment.GetShipmentsResponse> r = pushTaskService.execute(() -> {
                        R<com.renpho.erp.apiproxy.walmart.model.fulfillment.GetShipmentsResponse> ret = walmartInboundClient.getShipments(shopAccount, req);
                        //将异常抛出才能触发重试机制
                        if (!ret.isSuccess()) {
                            log.error("调用【WFS-获取入库单列表】接口异常：, trNo={}, 异常信息：{}", tr.getTrNo(), ret);
                            inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, ret);
                            throw new RuntimeException("API获取入库单失败，原因：%s".formatted(ret.getMessage()));
                        }
                        return ret;
                    }, inboundPushTask);

                    inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, r.getData().getHeaders(), r.getData());

                    List<com.renpho.erp.apiproxy.walmart.model.fulfillment.GetShipmentsResponse.PayloadItem> payload = r.getData().getPayload();
                    for (com.renpho.erp.apiproxy.walmart.model.fulfillment.GetShipmentsResponse.PayloadItem shipment : payload) {
                        String inboundStatus = shipment.getStatus();

                        //非签收中和已完成，不去获取item详情
                        if (!Set.of("RECEIVING_IN_PROGRESS", "CLOSED").contains(inboundStatus)) {
                            continue;
                        }

                        //获取item详情
                        SpringUtil.getBean(this.getClass()).handleWalmartShipmentItems(tr, shopAccount, inboundStatus, inboundPushTask);
                    }
                } catch (Exception e) {
                    log.error("拉取【Walmart入库单】失败, trNo={}", tr.getTrNo(), e);
                }
            }
        }
    }


    /**
     * 获取Walmart的入库单
     *
     * @param tr              TransportRequest
     * @param shopAccount     shopAccount
     * @param inboundStatus   入库单状态
     * @param inboundPushTask 入库单推送任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleWalmartShipmentItems(TransportRequest tr,
                                           com.renpho.erp.apiproxy.walmart.model.ShopAccount shopAccount,
                                           String inboundStatus,
                                           PushTask inboundPushTask) {
        GetInboundShipmentItemsRequest itemsRequest = new GetInboundShipmentItemsRequest();
        itemsRequest.setShipmentId(tr.getShipmentId());

        LocalDateTime triggerTime = LocalDateTime.now();

        //保存请求记录
        InboundRequestHistory history = inboundRequestHistoryService.add(tr, WarehouseProviderType.WFS, null, itemsRequest);
        R<GetInboundShipmentItemsResponse> ret = pushTaskService.execute(() -> {
            R<GetInboundShipmentItemsResponse> itemR = walmartInboundClient.getInboundShipmentItems(shopAccount, itemsRequest);
            //将异常抛出才能触发重试机制
            if (!itemR.isSuccess()) {
                log.error("获取【WFS】入库单详情接口异常：, trNo={}，异常原因：{}", tr.getTrNo(), itemR.getMessage());
                inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, itemR);
                throw new RuntimeException("API获取入库单失败，原因：%s".formatted(itemR.getMessage()));
            }
            return itemR;
        }, inboundPushTask);

        // 保存请求记录
        InboundRequestHistory itemHistory = inboundRequestHistoryService.add(tr, WarehouseProviderType.WFS, null,
                itemsRequest, CommonConstant.RESPONSE_SUCCESS_CODE, null, ret.getData());

        for (GetInboundShipmentItemsResponse.PayloadItem item : ret.getData().getPayload()) {
            InboundRecord record = inboundRecordConverter.toDomain(tr);
            record.setWarehouseType(WarehouseProviderType.WFS);
            record.setRequestHistoryId(itemHistory.getId());
            InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tr).orElse(null);

            record.setFnSku(item.getGtin());
            record.setReceivedTime(triggerTime);
            boolean isFirstReceived = tr.checkTrIsFirstReceived();
            if (isFirstReceived) {
                //设置TR单的开始签收时间
                String isoReceivingStartDateStr = item.getReceivingStartDate();
                if (StringUtil.isNotBlank(isoReceivingStartDateStr)) {

                    LocalDateTime receivedTime = DateUtils.toUtcLocalDateTime(isoReceivingStartDateStr);
                    tr.setReceivedTime(receivedTime);
                }
            }

            boolean haveNewReceived = record.buildReceivedInfo(tr.getItem().getQuantity(), lastRecord);
            record.setStatus(InboundStatus.FINISH);
            int putawayQty = item.getReceivedUnitsAtFc();
            record.setPutawayTime(triggerTime);

            //本次上架的不良品数量=上架的不良品总数量-历史上架的不良品总数量
            int badQty = item.getDamagedUnitsAtFc() - Optional.ofNullable(lastRecord).map(InboundRecord::getReceivedBadQuantityTotal).orElse(0);
            //上架总数量-不良品总数量=总的良品数量
            //总的良品数量-历史上架的良品总数量=本次上架的良品数量
            int goodQty =
                    putawayQty - item.getDamagedUnitsAtFc() - Optional.ofNullable(lastRecord).map(InboundRecord::getReceivedGoodQuantityTotal).orElse(0);
            boolean haveNewPutaway = record.buildPutawayInfo(putawayQty, goodQty, badQty, lastRecord);

            //记录tr单的实际签收数量和上架数量
            tr.getItem().setReceivedQuantity(tr.getItem().getQuantity());
            tr.getItem().setShelvedQuantity(putawayQty);


            if ("RECEIVING_IN_PROGRESS".equals(inboundStatus)) {
                record.setPutawayStatus(InboundStatus.WORKING);
                tr.setShipStatus(TransportOrderStatusEnum.DELIVERED_PART);
            } else if ("CLOSED".equals(inboundStatus)) {
                record.setPutawayStatus(InboundStatus.FINISH);
                //TR单出运状态更新“签收完成”
                tr.setShipStatus(TransportOrderStatusEnum.DELIVERED);
                tr.setReceivedEndTime(triggerTime);
            }

            tr.setSyncApiStatus(SyncApiStatus.SUCCESS);
            //存在增量数据
            if (haveNewReceived || haveNewPutaway) {
                //更新TR单
                transportRequestOrderRepository.updateById(tr);
                transportRequestOrderItemRepository.update(tr);
                // 判断此TR单对应的TO单下所有TR是否全部都是已签收状态，如果是，则将TO状态改为已签收
                transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.DELIVERED);

                if ("CLOSED".equals(inboundStatus)) {
                    //签收完成后，计算签收、上架差异数量
                    boolean haveDiscrepancy = tr.getItem().calculateTheDifferenceValue(tr.getItem().getReceivedQuantity(), tr.getItem().getShelvedQuantity());
                    log.info("{} 签收完成后，计算签收、上架差异数量:{}", tr.getTrNo(), haveDiscrepancy);
                    transportRequestOrderItemRepository.update(tr);
                    //差异值处理完后，若无差异，更新TR单出运状态更新“已完成"
                    if (!haveDiscrepancy) {
                        tr.setShipStatus(TransportOrderStatusEnum.COMPLETED);
                        //更新TR单
                        transportRequestOrderRepository.updateById(tr);
                        transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.COMPLETED);
                    } else {
                        //有签收差异，调用IMS，创建差异单
                        discrepancyService.handleTrDiscrepancy(tr);
                    }
                }


                //保存签收、上架数据
                record = inboundRecordRepository.add(record);
                inboundAllocationProducer.send(record);

                //首次签收推送签收消息到FMS
                transportRequestService.firstReceiveSendReceiveMsgToFms(tr.getToNo(), isFirstReceived);
            }
        }

    }

}
