package com.renpho.erp.tms.application.transferorder;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.renpho.erp.apiproxy.amazon.model.ShopAccount;
import com.renpho.erp.apiproxy.amazon.model.awd.GetInboundShipmentV20240509Request;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.tms.application.discrepancy.DiscrepancyService;
import com.renpho.erp.tms.application.inbound.InboundRecordQueryService;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.domain.inbound.*;
import com.renpho.erp.tms.domain.store.Store;
import com.renpho.erp.tms.domain.transferorder.*;
import com.renpho.erp.tms.domain.transportrequest.PushTask;
import com.renpho.erp.tms.domain.transportrequest.PushTaskStatus;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.erp.tms.infrastructure.common.constant.CommonConstant;
import com.renpho.erp.tms.infrastructure.persistence.inbound.po.converter.InboundRecordConverter;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.repository.TransferOrderItemRepository;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.ShopAccountBuilder;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.inbound.AmazonInboundClient;
import com.renpho.erp.tms.infrastructure.remote.store.repository.StoreLookup;
import com.renpho.karma.dto.R;
import io.swagger.client.model.awd.DistributionPackage;
import io.swagger.client.model.awd.DistributionPackageContents;
import io.swagger.client.model.awd.InboundShipment;
import io.swagger.client.model.awd.InboundShipmentStatus;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/8/29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransferOrderAWDInboundService {
    private final TransferOrderQueryService transferOrderQueryService;
    private final StoreLookup storeLookup;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final InboundRecordRepository inboundRecordRepository;
    private final InboundRecordQueryService inboundRecordQueryService;
    private final InboundRecordConverter inboundRecordConverter;
    private final PushTaskService pushTaskService;
    private final AmazonInboundClient amazonInboundClient;
    private final TransferOrderRepository transferOrderRepository;
    private final TransferOrderItemRepository transferOrderItemRepository;
    private final TransferOrderStatusHistoryRepository transferOrderStatusHistoryRepository;
    private final DiscrepancyService discrepancyService;


    //AWD签收状态
    final List<InboundShipmentStatus> AWD_RECEIVED_STATUS = Arrays.asList(InboundShipmentStatus.DELIVERED,
            InboundShipmentStatus.RECEIVING, InboundShipmentStatus.CLOSED);
    //AWD上架状态
    final List<InboundShipmentStatus> AWD_ON_SHELVES_STATUS = Arrays.asList(InboundShipmentStatus.RECEIVING,
            InboundShipmentStatus.CLOSED);

    /**
     * 获取AWD的Shipments
     * @param tsNos TS单号
     * @param tsIds TS单ID
     * @param syncApiStatus 同步API状态
     */
    public void pullShipmentsFromAWDForTS(List<String> tsNos, List<TransferOrderId> tsIds, String syncApiStatus) {
        //“已发货”和“部分签收”状态的AWD TS单
        List<TransferOrder> awdTsList = transferOrderQueryService.findByWarehouseProviderType(
                WarehouseProviderType.AWD,
                tsIds,
                tsNos,
                List.of(TransferOrderStatus.SHIPPED, TransferOrderStatus.RECEIVING),
                syncApiStatus
        ).stream().filter(ts -> StringUtil.isNotBlank(ts.getShipmentId())).toList();

        //将awdTrs按照Store进行分组
        Map<Store, List<TransferOrder>> shipmentsGroupByStore = CollectionUtil.emptyIfNull(awdTsList).stream()
                .collect(Collectors.groupingBy(TransferOrder::getStore));

        for (Map.Entry<Store, List<TransferOrder>> entry : shipmentsGroupByStore.entrySet()) {
            List<TransferOrder> trs = entry.getValue();
            Store store = entry.getKey();
            StoreAuthorizationVo authorization = storeLookup.getAuthorization(store);
            ShopAccount shopAccount = ShopAccountBuilder.buildAmzShopAccount(authorization.getAuthorization());
            //获取入库单详情
            for (TransferOrder ts : trs) {
                fetchAndProcessShipmentDetails(ts, shopAccount);
            }
        }

    }

    private void fetchAndProcessShipmentDetails(TransferOrder ts, ShopAccount shopAccount) {
        GetInboundShipmentV20240509Request request = new GetInboundShipmentV20240509Request();
        request.setSkuQuantities("SHOW");

        try {
            // 保存请求记录
            InboundRequestHistory history = inboundRequestHistoryService.add(ts, WarehouseProviderType.AWD, null, ts.getShipmentId());
            PushTask inboundPushTask = pushTaskService.inbound(ts, PushTaskStatus.PENDING, 0, null);

            LocalDateTime triggerTime = LocalDateTime.now();
            R<InboundShipment> ret = pushTaskService.execute(() -> {
                R<InboundShipment> r = amazonInboundClient.getInboundShipment(shopAccount, ts.getShipmentId(), request);
                //将异常抛出才能触发重试机制
                if (!r.isSuccess()) {
                    log.error("调用【AWD-入库单列表】接口异常：, tsNo={}，异常信息：{}", ts.getTsNo(), r);
                    inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                    throw new RuntimeException("API获取入库单失败，原因：%s".formatted(r.getMessage()));
                }
                return r;
            }, inboundPushTask);

            inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null, ret.getData());

            SpringUtil.getBean(this.getClass()).handleInboundData(ts, ret.getData(), history.getId(), triggerTime);
        } catch (Exception e) {
            log.error("拉取【AWD-入库单】失败, tsNo={}", ts.getTsNo(), e);
        }
    }

    /**
     * 处理AWD入库单入库 数据
     *
     * @param ts              ts单
     * @param inboundShipment 入库单详情
     * @param historyId       请求历史ID
     * @param triggerTime     触发时间
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleInboundData(TransferOrder ts,
                                  InboundShipment inboundShipment,
                                  InboundRequestHistoryId historyId,
                                  LocalDateTime triggerTime) {
        InboundShipmentStatus shipmentStatus = inboundShipment.getShipmentStatus();

        if (!AWD_ON_SHELVES_STATUS.contains(shipmentStatus) && !AWD_RECEIVED_STATUS.contains(shipmentStatus)) {
            log.warn("入库状态不符合签收上架要求，不做处理");
            return;
        }

        if (Optional.of(inboundShipment).map(InboundShipment::getShipmentContainerQuantities).isEmpty()) {
            log.warn("入库单详情为空, tsNo={}", ts.getTsNo());
            return;
        }
        
        // 将 shipmentContainerQuantities 转换为 Map<SKU, Count>，键为sellerSKU，值为sku的签收数量
        Map<String, Integer> skuCountMap = inboundShipment.getShipmentContainerQuantities().stream()
                .flatMap(dpq -> Optional.ofNullable(dpq.getDistributionPackage())
                        .map(DistributionPackage::getContents)
                        .map(DistributionPackageContents::getProducts)
                        .orElse(new ArrayList<>())
                        .stream()
                        .map(pq -> Map.entry(pq.getSku(), pq.getQuantity() * Optional.ofNullable(dpq.getCount()).orElse(0))))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        Integer::sum
                ));

        for (TransferOrderItem tsItem : ts.getItems()) {
            if (!skuCountMap.containsKey(tsItem.getSellerSku())) {
                continue;
            }
            //上架数量
            Integer putawayQty = skuCountMap.get(tsItem.getSellerSku());

            processItem(putawayQty, shipmentStatus, tsItem, triggerTime, historyId);
        }
        //处理完签收逻辑后，处理 TS 和 TS Item 的更新
        transferOrderItemRepository.batchUpdate(ts.getItems());

        if (AWD_RECEIVED_STATUS.contains(shipmentStatus)) {
            ts.setStatus(TransferOrderStatus.RECEIVING);
            transferOrderStatusHistoryRepository.addHistoryStatus(ts.getId().id(), ts.getTsNo(), TransferOrderStatus.RECEIVING.getValue());
        }

        if (InboundShipmentStatus.CLOSED.equals(shipmentStatus)) {
            ts.setStatus(TransferOrderStatus.CLOSED);
            transferOrderStatusHistoryRepository.addHistoryStatus(ts.getId().id(), ts.getTsNo(), TransferOrderStatus.RECEIVED.getValue());

            // 处理TS单的签收差异
            discrepancyService.handleTsDiscrepancy(ts);
        }

        ts.setSyncApiStatus(SyncApiStatus.SUCCESS);
        //更新TS单
        transferOrderRepository.updateById(ts);

    }

    private void processItem(Integer putawayQty,
                             InboundShipmentStatus shipmentStatus,
                             TransferOrderItem tsItem,
                             LocalDateTime triggerTime,
                             InboundRequestHistoryId historyId) {
        // 查询上一次记录，用于差异计算
        InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tsItem).orElse(null);

        InboundRecord record = inboundRecordConverter.transferOrderItemToInboundRecord(tsItem);
        record.setWarehouseType(WarehouseProviderType.AWD);
        record.setRequestHistoryId(historyId);
        boolean haveNewReceived = false, haveNewPutaway = false;

        boolean isFirstReceived = tsItem.checkIsFirstReceived();
        if (AWD_RECEIVED_STATUS.contains(shipmentStatus)) {
            //签收数量：取TS的发货数量【不取接口的】
            int receivedQty = tsItem.getQty();
            haveNewReceived = record.buildReceivedInfo(receivedQty, lastRecord);
            record.setStatus(InboundStatus.FINISH);
            record.setPutawayStatus(InboundStatus.PENDING);
            record.setReceivedTime(triggerTime);
            if (isFirstReceived) {
                //设置TS单的开始签收时间
                tsItem.setReceivedStartTime(triggerTime);
            }
            //记录tr单的实际签收数量
            tsItem.setReceivedQty(receivedQty);
        }

        if (AWD_ON_SHELVES_STATUS.contains(shipmentStatus)) {
            //上架
            haveNewPutaway = record.buildPutawayInfo(putawayQty, lastRecord);
            record.setPutawayTime(triggerTime);
            record.setPutawayStatus(InboundStatus.WORKING);
            //记录ts单的实际上架数量
            tsItem.setPutawayQty(putawayQty);
            // 记录上架完成时间
            tsItem.setReceivedEndTime(triggerTime);
            tsItem.setPutawayStartTime(triggerTime);
        }
        if (InboundShipmentStatus.CLOSED.equals(shipmentStatus)) {
            record.setPutawayStatus(InboundStatus.FINISH);
            tsItem.calculateTheDifferenceValue(tsItem.getReceivedQty(), tsItem.getPutawayQty());
            tsItem.setPutawayEndTime(triggerTime);
        }

        //存在增量数据
        if (haveNewReceived || haveNewPutaway) {
            //保存签收、上架数据
            inboundRecordRepository.add(record);
        }
    }
}
