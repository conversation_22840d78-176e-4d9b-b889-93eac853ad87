package com.renpho.erp.tms.application.api.yunwms.strategy;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.renpho.erp.apiproxy.eccang.yunwms.model.instock.*;
import com.renpho.erp.apiproxy.eccang.yunwms.model.transfer.*;
import com.renpho.erp.tms.application.discrepancy.DiscrepancyService;
import com.renpho.erp.tms.application.inbound.InboundAllocationProducer;
import com.renpho.erp.tms.application.inbound.InboundRecordQueryService;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transportorder.TransportOrderService;
import com.renpho.erp.tms.application.transportrequest.PushTaskQueryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestService;
import com.renpho.erp.tms.application.transportrequest.stream.TransportRequestProducer;
import com.renpho.erp.tms.domain.inbound.*;
import com.renpho.erp.tms.domain.transportorder.TransportOrderStatusEnum;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.common.constant.YunWmsConstant;
import com.renpho.erp.tms.infrastructure.common.converter.MultiLanguageConverter;
import com.renpho.erp.tms.infrastructure.persistence.inbound.po.converter.InboundRecordConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderItemRepository;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;
import com.renpho.erp.tms.infrastructure.util.DateUtils;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * TransportRequest入库单生成策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransportRequestYunWmsInboundStrategy implements YunWmsInboundOrderStrategy<TransportRequest> {

    private final PushTaskService pushTaskService;
    private final PushTaskQueryService pushTaskQueryService;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final MultiLanguageConverter multiLanguageConverter;
    private final TransportRequestOrderRepository transportRequestOrderRepository;
    private final TransportRequestProducer transportRequestProducer;
    private final TransportRequestService transportRequestService;
    private final InboundRecordQueryService inboundRecordQueryService;
    private final InboundRecordConverter inboundRecordConverter;
    private final TransportRequestOrderItemRepository transportRequestOrderItemRepository;
    private final InboundRecordRepository inboundRecordRepository;
    private final InboundAllocationProducer inboundAllocationProducer;
    private final TransportOrderService transportOrderService;
    private final DiscrepancyService discrepancyService;

    @Override
    public TransferOrderSaveRequest buildCreateTransferInboundRequest(TransportRequest tr) {
        TransferOrderSaveRequest request = new TransferOrderSaveRequest();
        request.setAddService(0);
        request.setTransferWay(1);
        request.setWarehouseNo(tr.getDestWarehouse().getThirdWarehouseCode());
        request.setReferenceNo(tr.getPoNo());
        Date date = Date.from(tr.getCreated().getOperateTime().atZone(ZoneId.systemDefault()).toInstant());
        DateTime predictArriveTime = DateUtil.offsetDay(date, 70);
        request.setPredictArriveTime(DateUtil.format(predictArriveTime, DatePattern.NORM_DATE_PATTERN));

        TransportRequestItem trItem = tr.getItem();
        TransferOrderSaveRequest.InboundBoxDTO inboundItem = new TransferOrderSaveRequest.InboundBoxDTO();
        inboundItem.setBoxMark(tr.getPoNo());
        inboundItem.setHeight(trItem.getProduct().getActiveBoxSpec().getBoxHeightMetric().doubleValue());
        inboundItem.setWidth(trItem.getProduct().getActiveBoxSpec().getBoxWidthMetric().doubleValue());
        inboundItem.setLength(trItem.getProduct().getActiveBoxSpec().getBoxLengthMetric().doubleValue());
        inboundItem.setSkuDesc(trItem.getProduct().getPsku());
        inboundItem.setTotalBoxNum(trItem.getBoxQty().doubleValue());
        inboundItem.setWeight(tr.getTotalGrossWeight().doubleValue());

        TransferOrderSaveRequest.InboundSkuDTO skuItem = new TransferOrderSaveRequest.InboundSkuDTO();
        skuItem.setProductCode(tr.getItem().getProduct().getFnSku());
        String cnName = multiLanguageConverter.findCnName(tr.getProduct().getNames());
        skuItem.setProductDesc(cnName);
        skuItem.setSingleNum(trItem.getQuantityPerBox());
        inboundItem.setSkuList(List.of(skuItem));

        request.setInboundList(List.of(inboundItem));
        return request;
    }

    @Override
    public TransferOrderLabelRenderBoxMarkRequest buildBoxLabelRequest(TransportRequest tr) {
        TransferOrderLabelRenderBoxMarkRequest req = new TransferOrderLabelRenderBoxMarkRequest();
        req.setTransferNo(tr.getShipmentId());
        req.setBoxMarkType(1);
        req.setTemplateId(14721);
        return req;
    }

    @Override
    public TransferOrderCancelRequest buildCancelTransferOrderRequest(TransportRequest tr) {
        TransferOrderCancelRequest req = new TransferOrderCancelRequest();
        req.setBusinessNo(tr.getShipmentId());
        return req;
    }

    @Override
    public GetTransferOrderInfoRequest buildGetTransferOrderInfoRequest(TransportRequest tr) {
        GetTransferOrderInfoRequest req = new GetTransferOrderInfoRequest();
        req.setTransferNo(tr.getShipmentId());
        return req;
    }

    /**
     * 构建入库记录
     */
    private InboundRecord buildInboundRecord(TransportRequest tr, InboundRequestHistoryId historyId,
                                             int receivedQuantity, InboundRecord lastRecord, boolean isLastReceived) {
        InboundRecord record = inboundRecordConverter.toDomain(tr);
        record.setWarehouseType(WarehouseProviderType.POLARIS_YUNWMS);
        record.setRequestHistoryId(historyId);
        record.setStatus(isLastReceived ? InboundStatus.FINISH : InboundStatus.WORKING);
        record.setPutawayStatus(isLastReceived ? InboundStatus.FINISH : InboundStatus.WORKING);

        record.buildReceivedInfo(receivedQuantity, lastRecord);
        record.buildPutawayInfo(receivedQuantity, lastRecord);

        return record;
    }

    @Override
    public CreateAsnRequest buildCreateAsnRequest(TransportRequest tr) {
        CreateAsnRequest request = new CreateAsnRequest();
        request.setReference_no(tr.getTrNo());
        request.setIncome_type(0);
        request.setReceiving_type("D");
        request.setWarehouse_code(tr.getDestWarehouse().getThirdWarehouseCode());
        request.setReceiving_desc(tr.getPoNo());
        request.setSpontaneous_head_cheng_type(2);
        request.setBulk_cargo_type_piece(1);
        request.setStock_type(0);

        List<CreateAsnRequest.ReceivingItemDTO> items = new ArrayList<>();
        int boxNos = tr.getBoxQty().intValue();
        for (int i = 1; i <= boxNos; i++) {
            //注意：：：如果后面一个TR对应多个sku，则需要调整下面逻辑
            CreateAsnRequest.ReceivingItemDTO itemDTO = new CreateAsnRequest.ReceivingItemDTO();
            itemDTO.setProduct_sku(tr.getItem().getProduct().getFnSku());
            itemDTO.setQuantity(tr.getItem().getQuantityPerBox());
            itemDTO.setBox_no(i);
            itemDTO.setInventory_type(0);
            items.add(itemDTO);
        }

        request.setItems(items);

        return request;
    }

    @Override
    public GetReceivingBoxPdfByCodeRequest buildGetAsnBoxLabelRequest(TransportRequest tr) {
        GetReceivingBoxPdfByCodeRequest req = new GetReceivingBoxPdfByCodeRequest();
        req.setReceiving_code(tr.getShipmentId());
        req.setPdf_type("box");
        req.setPdf_size("100*100-PT");
        req.setMade_in_china(1);
        req.setContent_type("url");
        return req;
    }


    @Override
    public CancelAsnRequest buildCancelAsnRequest(TransportRequest tr) {
        CancelAsnRequest req = new CancelAsnRequest();
        req.setReceiving_code(tr.getShipmentId());
        return req;
    }

    @Override
    public CetAsnListRequest buildGetAsnListRequest(TransportRequest tr) {
        CetAsnListRequest req = new CetAsnListRequest();
        req.setReceiving_code(tr.getShipmentId());
        return req;
    }

    @Override
    public PushTask createPushTask(TransportRequest tr) {
        return pushTaskService.createInbound(tr, PushTaskStatus.PENDING, 0, null);
    }

    @Override
    public PushTask getCartonFileGenerationTask(TransportRequest tr) {
        return pushTaskQueryService.findByBizAndType(tr, List.of(PushTaskType.CARTON_FILE_GENERATION))
                .stream().findFirst()
                .orElseThrow(() -> new RuntimeException("找不到类型=CARTON_FILE_GENERATION的task数据, trNo=" + tr.getTrNo()));
    }

    @Override
    public InboundRequestHistory addRequestHistory(TransportRequest tr, Object request) {
        return inboundRequestHistoryService.add(tr, WarehouseProviderType.POLARIS_YUNWMS, null, request);
    }

    @Override
    public InboundRequestHistory updateRequestHistory(InboundRequestHistoryId historyId, Integer responseCode,
                                                      Object responseHeaders, Object responseBody) {
        return inboundRequestHistoryService.update(historyId, responseCode, responseHeaders, responseBody);
    }

    @Override
    public void updateOrder(TransportRequest tr, String transferNo) {
        tr.setShipmentId(transferNo);
        tr.setSyncApiStatus(SyncApiStatus.SUCCESS);
        transportRequestOrderRepository.updateById(tr);
    }

    @Override
    public void updateCartonLabelFiles(TransportRequest tr, List<String> fileIds) {
        tr.setCartonLabelFileIds(fileIds);
        tr.setStatus(TransportRequestStatus.PENDING_CONSOLIDATION);
        tr.setSyncApiStatus(SyncApiStatus.SUCCESS);
        transportRequestOrderRepository.updateById(tr);
    }

    @Override
    public void sendCartonFilesMessage(TransportRequest tr) {
        transportRequestProducer.sendTrCartonFiles(tr);
    }

    @Override
    public String getOrderNo(TransportRequest tr) {
        return tr.getTrNo();
    }

    @Override
    public PushTask createCancelTask(TransportRequest tr) {
        return pushTaskService.cancel(tr, PushTaskStatus.PENDING, 0, null);
    }

    @Override
    public void clearShipmentId(TransportRequest tr) {
        transportRequestService.clearShipmentIdById(tr.getId().id());
    }

    @Override
    public PushTask createInboundPushTask(TransportRequest tr) {
        return pushTaskService.inbound(tr, PushTaskStatus.PENDING, 0, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleTransferOrderData(GetTransferOrderInfoResponse data, TransportRequest tr, InboundRequestHistoryId historyId) {
        Integer status = Integer.parseInt(data.getStatus());
        if (!YunWmsConstant.TransferOrderStatus.COMPLETED.equals(status)) {
            return;
        }

        InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tr).orElse(null);
        List<GetTransferOrderInfoResponse.ReceiveRecords> receiveRecords = data.getReceiveRecords();

        for (int i = 0; i < receiveRecords.size(); i++) {
            processReceiveRecord(receiveRecords.get(i), tr, historyId, lastRecord, i, receiveRecords.size());
        }
    }

    /**
     * 处理单次签收记录
     */
    private void processReceiveRecord(GetTransferOrderInfoResponse.ReceiveRecords receiveRecord,
                                      TransportRequest tr, InboundRequestHistoryId historyId,
                                      InboundRecord lastRecord, int index, int totalCount) {
        int receivedQuantity = calculateReceivedQuantity(receiveRecord, tr);
        //第一次签收
        boolean isFirstReceived = index == 0;
        //最后一次签收
        boolean isLastReceived = index == totalCount - 1;

        InboundRecord record = buildInboundRecord(tr, historyId, receivedQuantity, lastRecord, isLastReceived);
        setReceiveTime(receiveRecord, record, tr, isFirstReceived, isLastReceived);

        updateTransportRequest(tr, receivedQuantity);

        if (isLastReceived) {
            handleLastReceive(tr);
        }

        //保存签收、上架数据
        saveRecordAndSendMessage(record, tr, isFirstReceived);
    }

    /**
     * 计算签收数量
     */
    private int calculateReceivedQuantity(GetTransferOrderInfoResponse.ReceiveRecords receiveRecord, TransportRequest tr) {
        //签收数量：receiveNum * TR的装箱数量
        return Integer.parseInt(receiveRecord.getReceiveNum()) * tr.getItem().getProduct().getQuantityPerBox();
    }

    /**
     * 设置签收时间
     */
    private void setReceiveTime(GetTransferOrderInfoResponse.ReceiveRecords receiveRecord, InboundRecord record,
                                TransportRequest tr, boolean isFirstReceived, boolean isLastReceived) {
        if (StringUtil.isBlank(receiveRecord.getEcCreateTime())) {
            return;
        }

        LocalDateTime receivedTime = DateUtils.toUtcLocalDateTime(receiveRecord.getEcCreateTime());
        record.setReceivedTime(receivedTime);
        record.setPutawayTime(receivedTime);

        if (isFirstReceived) {
            tr.setReceivedTime(receivedTime);
        }
        if (isLastReceived) {
            tr.setReceivedEndTime(receivedTime);
            tr.setShipStatus(TransportOrderStatusEnum.DELIVERED);
        }
    }

    /**
     * 更新TR单信息
     */
    private void updateTransportRequest(TransportRequest tr, int receivedQuantity) {
        tr.getItem().setReceivedQuantity(receivedQuantity);
        tr.getItem().setShelvedQuantity(receivedQuantity);
        tr.setSyncApiStatus(SyncApiStatus.SUCCESS);

        transportRequestOrderRepository.updateById(tr);
        transportRequestOrderItemRepository.update(tr);
        // 判断此TR单对应的TO单下所有TR是否全部都是已签收状态，如果是，则将TO状态改为已签收
        transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.DELIVERED);
    }

    /**
     * 处理最后一次签收
     */
    private void handleLastReceive(TransportRequest tr) {
        //签收完成后，计算签收、上架差异数量
        boolean haveDiscrepancy = tr.getItem().calculateTheDifferenceValue(
                tr.getItem().getReceivedQuantity(), tr.getItem().getShelvedQuantity());
        transportRequestOrderItemRepository.update(tr);

        //差异值处理完后，若无差异，更新TR单出运状态更新“已完成"
        if (!haveDiscrepancy) {
            tr.setShipStatus(TransportOrderStatusEnum.COMPLETED);
            //更新TR单
            transportRequestOrderRepository.updateById(tr);
            transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.COMPLETED);
        } else {
            //有签收差异，调用IMS，创建差异单
            discrepancyService.handleTrDiscrepancy(tr);
        }
    }

    /**
     * 保存记录并发送消息
     */
    private void saveRecordAndSendMessage(InboundRecord record, TransportRequest tr, boolean isFirstReceived) {
        record = inboundRecordRepository.add(record);
        inboundAllocationProducer.send(record);

        //首次签收推送签收消息到FMS
        transportRequestService.firstReceiveSendReceiveMsgToFms(tr.getToNo(), isFirstReceived);
    }

    @Override
    public void updateAsnCartonLabelFiles(TransportRequest tr, List<String> fileIds) {
        tr.setCartonLabelFileIds(fileIds);
        tr.setStatus(TransportRequestStatus.PENDING_CONSOLIDATION);
        tr.setSyncApiStatus(SyncApiStatus.SUCCESS);
        transportRequestOrderRepository.updateById(tr);
    }

    @Override
    public PushTask createCancelAsnTask(TransportRequest tr) {
        return pushTaskService.cancel(tr, PushTaskStatus.PENDING, 0, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleAsnListData(List<GetAsnListResponse.AsnListData> dataList, TransportRequest tr, InboundRequestHistoryId historyId) {
        dataList.forEach(asn -> processAsnListData(asn, tr, historyId));
    }

    private void processAsnListData(GetAsnListResponse.AsnListData data,
                                    TransportRequest tr,
                                    InboundRequestHistoryId historyId) {
        String receivingStatus = data.getReceiving_status();
        InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tr).orElse(null);

        int receivedQty = data.getItems().stream().map(GetAsnListResponse.Item::getReceived_quantity).mapToInt(Integer::parseInt).sum();
        int putawayQty = data.getItems().stream().mapToInt(GetAsnListResponse.Item::getPutaway_quantity).sum();

        InboundRecord record = inboundRecordConverter.toDomain(tr);
        boolean isFirstReceived = tr.checkTrIsFirstReceived();
        boolean haveNewReceived = false, haveNewPutaway = false;

        if (Set.of(YunWmsConstant.InboundOrderStatus.RECEIVE_COMPLETED_DEST,
                YunWmsConstant.InboundOrderStatus.COMPLETED_PUTAWAY).contains(receivingStatus)) {
            record.setWarehouseType(WarehouseProviderType.POLARIS_YUNWMS);
            record.setRequestHistoryId(historyId);
            record.setStatus(InboundStatus.WORKING);
            record.setPutawayStatus(InboundStatus.WORKING);
            tr.setShipStatus(TransportOrderStatusEnum.DELIVERED_PART);

            haveNewReceived = record.buildReceivedInfo(receivedQty, lastRecord);
            haveNewPutaway = record.buildPutawayInfo(putawayQty, lastRecord);

            String warehouseReceivingCompleteTime = data.getWarehouse_receiving_complete_time();
            if (StringUtil.isNotBlank(warehouseReceivingCompleteTime)) {
                LocalDateTime receivedTime = DateUtils.toUtcLocalDateTime(warehouseReceivingCompleteTime);
                record.setReceivedTime(receivedTime);
                if (isFirstReceived) {
                    tr.setReceivedTime(receivedTime);
                }
            }

            String warehouseShelfTime = data.getWarehouse_shelf_time();
            if (StringUtil.isNotBlank(warehouseShelfTime)) {
                LocalDateTime shelfTime = DateUtils.toUtcLocalDateTime(warehouseShelfTime);
                record.setPutawayTime(shelfTime);
            }

            tr.getItem().setReceivedQuantity(record.getReceivedQuantityTotal());
            tr.getItem().setShelvedQuantity(record.getPutawayQuantityTotal());

            if (YunWmsConstant.InboundOrderStatus.COMPLETED_PUTAWAY.equals(receivingStatus)) {
                tr.setShipStatus(TransportOrderStatusEnum.DELIVERED);
                tr.setReceivedEndTime(record.getReceivedTime());
            }
        }

        if (haveNewReceived || haveNewPutaway) {
            tr.setSyncApiStatus(SyncApiStatus.SUCCESS);
            transportRequestOrderRepository.updateById(tr);
            transportRequestOrderItemRepository.update(tr);
            transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.DELIVERED);

            if (YunWmsConstant.InboundOrderStatus.COMPLETED_PUTAWAY.equals(receivingStatus)) {
                record.setStatus(InboundStatus.FINISH);
                record.setPutawayStatus(InboundStatus.FINISH);

                handleLastReceive(tr);
            }

            saveRecordAndSendMessage(record, tr, isFirstReceived);
        }
    }

}
