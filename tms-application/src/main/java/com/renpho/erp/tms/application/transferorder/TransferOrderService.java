package com.renpho.erp.tms.application.transferorder;

import com.renpho.erp.apiproxy.eccang.yunwms.model.transfer.GetTransferOrderInfoResponse;
import com.renpho.erp.bpm.client.process.vo.ProcessInstanceRecordVO;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.tms.application.inbound.InboundRecordQueryService;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.inbound.*;
import com.renpho.erp.tms.domain.orderfile.*;
import com.renpho.erp.tms.domain.processinstance.ProcessInstanceId;
import com.renpho.erp.tms.domain.product.Product;
import com.renpho.erp.tms.domain.transferorder.*;
import com.renpho.erp.tms.domain.transportorder.ApprovalStatus;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.erp.tms.infrastructure.common.constant.YunWmsConstant;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import com.renpho.erp.tms.infrastructure.persistence.inbound.po.converter.InboundRecordConverter;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.repository.TransferOrderItemRepository;
import com.renpho.erp.tms.infrastructure.remote.inventory.repository.InventoryRepository;
import com.renpho.erp.tms.infrastructure.remote.processinstance.ProcessInstanceRepository;
import com.renpho.erp.tms.infrastructure.remote.processinstance.form.TransferOrderBpmForm;
import com.renpho.erp.tms.infrastructure.remote.product.repository.ProductLookup;
import com.renpho.erp.tms.infrastructure.util.DateUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/8/23
 */
@Service
@RequiredArgsConstructor
public class TransferOrderService {

    private final TransferOrderRepository transferOrderRepository;
    private final TransferOrderItemRepository transferOrderItemRepository;
    private final TransferOrderLookup transferOrderLookup;
    private final InboundRecordQueryService inboundRecordQueryService;
    private final InboundRecordRepository inboundRecordRepository;
    private final InboundRecordConverter inboundRecordConverter;
    private final InventoryRepository inventoryRepository;
    private final ProcessInstanceRepository processInstanceRepository;
    private final TransferOrderStatusHistoryRepository transferOrderStatusHistoryRepository;
    private final OrderFileRepository orderFileRepository;
    private final ProductLookup productLookup;

    @Transactional(rollbackFor = Exception.class)
    public TransferOrder add(TransferOrder command) {
        // 根据sku获取产品详情
        Map<String, Product> products = productLookup.findByPskus(command.getItems().stream().map(TransferOrderItem::getPsku).toList());
        // 计算明细行装箱数量、体积、毛重、净重
        for (TransferOrderItem item : command.getItems()) {
            Product product = products.get(item.getPsku());
            item.fillProduct(product);
        }
        // 计算单维度总箱数、总体积、总毛重、总净重
        command.sum();
        inventoryRepository.lock(command);
        return transferOrderRepository.add(command);
    }

    /**
     * 根据TS ID列表查询TS单信息
     *
     * @param tsIds TS ID列表
     * @return TS单信息
     */
    public List<TransferOrder> findByIds(List<TransferOrderId> tsIds) {
        Map<TransferOrderId, TransferOrder> domains = transferOrderLookup.findByIds(tsIds);
        //findAssociations(domains);
        return domains.values().stream().toList();
    }

    /**
     * 根据TS单号查询TS单信息
     *
     * @param tsNo TS单号
     * @return TS单信息
     */
    public TransferOrder findByTsNo(String tsNo) {
        return transferOrderRepository.findByTsNo(tsNo);
    }

    /**
     * 根据TR的ID集合查询tr上下文信息
     *
     * @param idList TR的ID集合
     * @return TR单信息
     */
    public Map<Integer, TransferOrder> getTsMapByIds(List<Integer> idList) {
        List<TransferOrderId> tsIds = idList.stream().map(TransferOrderId::new).toList();
        List<TransferOrder> tsList = this.findByIds(tsIds);
        return tsList.stream().collect(Collectors.toMap(TransferOrder::sourceId, a -> a));
    }

    @Transactional(rollbackFor = Exception.class)
    public TransferOrder update(TransferOrder command) {
        // TODO 解锁库存
        inventoryRepository.unlock(command);
        // TODO 锁定库存
        inventoryRepository.lock(command);
        return transferOrderRepository.update(command);
    }

    /**
     * 根据TS状态查询TS单信息
     *
     * @param statusList    TS状态列表
     * @param warehouseType 仓库类型
     * @return TS单信息
     */
    public List<TransferOrderData> selectTsListByStatusList(List<TransferOrderStatus> statusList, WarehouseProviderType warehouseType) {
        return transferOrderRepository.selectTsListByShipStatusList(statusList, warehouseType);
    }

    @LogRecord(module = LogModule.TRANSFER_ODER, type = LogModule.CommonDesc.EDIT_OPERATOR, desc = LogModule.TransferOrder.CANCEL_TS_DESC)
    public void clearShipmentIdById(TransferOrderId id) {
        Optional<TransferOrder> oldData = transferOrderLookup.findById(id);
        transferOrderRepository.clearShipmentIdById(id);
        Optional<TransferOrder> newData = transferOrderLookup.findById(id);
        LogRecordContextHolder.putRecordData(String.valueOf(id), oldData, newData);
    }

    public void handleTransferOrderData(GetTransferOrderInfoResponse data,
                                        TransferOrder ts,
                                        InboundRequestHistoryId historyId) {
        Integer status = Integer.parseInt(data.getStatus());
        if (!YunWmsConstant.TransferOrderStatus.COMPLETED.equals(status)) {
            return;
        }


        //将itemData按照FulfillmentNetworkSKU进行分组，返回一个Map，key为FulfillmentNetworkSKU，value为InboundShipmentItem
        Map<String, GetTransferOrderInfoResponse.Inbound> inboundDataMap = data.getInboundList().stream().collect(
                Collectors.toMap(
                        GetTransferOrderInfoResponse.Inbound::getSkuDesc,
                        Function.identity(),
                        (v, v2) -> v
                )
        );

        //获取签收时间
        String receivedTimeStr =
                data.getReceiveRecords().stream().findFirst()
                        .map(GetTransferOrderInfoResponse.ReceiveRecords::getEcCreateTime)
                        .orElse("0000-00-00 00:00:00");
        LocalDateTime receivedTime = DateUtils.toUtcLocalDateTime(receivedTimeStr);

        for (TransferOrderItem tsItem : ts.getItems()) {
            if (!inboundDataMap.containsKey(tsItem.getPsku())) {
                continue;
            }
            GetTransferOrderInfoResponse.Inbound shipmentItem = inboundDataMap.get(tsItem.getPsku());

            // 查询上一次记录，用于差异计算
            InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tsItem).orElse(null);

            processItem(shipmentItem, tsItem, historyId, lastRecord, receivedTime);


            //处理完签收逻辑后，处理 TS 和 TS Item 的更新
            transferOrderItemRepository.batchUpdate(ts.getItems());

            //todo TS单状态记录
            ts.setStatus(TransferOrderStatus.RECEIVING);
            ts.setStatus(TransferOrderStatus.RECEIVED);

            ts.setSyncApiStatus(SyncApiStatus.SUCCESS);
            //更新TS单
            transferOrderRepository.updateById(ts);
        }

    }

    /**
     * 接收bpm回调，变更调拨单状态
     *
     * <AUTHOR>
     * @since 2025/8/28 16:39
     */

    @Transactional(rollbackFor = Exception.class)
    public void updateTransferOrderApprovalStatus(String instanceId, Integer result, Integer updateBy) {

        // 判断 instanceId 是否存在
        ApprovalStatus approvalResult = ApprovalStatus.fromValue(result);
        if (approvalResult == null) {
            throw new BusinessException("INVALID_ENUM_VALUE", "ApprovalStatus 未配置值为 %d 的枚举值".formatted(result));
        }
        List<TransferOrder> orders = transferOrderLookup.findByProcessInstanceId(instanceId);
        if (CollectionUtils.isEmpty(orders)) {
            throw new BusinessException("error.ts.approval.not-exist", ", 审批单ID: " + instanceId);
        }

        orders.forEach(order -> {
            TransferOrderStatus status = order.determineFinalStatus(order, Objects.requireNonNull(ApprovalStatus.fromValue(result)));
            transferOrderRepository.updateApprovalResultByInstanceId(order.getTsId(), status, updateBy);
            transferOrderStatusHistoryRepository.addHistoryStatus(order.getId().id(), order.getTsNo(), order.getStatus().getValue(), updateBy);
        });
    }


    /**
     * 处理单个ITEM 的签收与上架逻辑，并记录入库信息。
     *
     * @param shipmentItem ShipmentItem
     * @param tsItem       TS Item
     * @param historyId    历史ID
     * @param lastRecord   上一次记录
     */
    private void processItem(GetTransferOrderInfoResponse.Inbound shipmentItem,
                             TransferOrderItem tsItem,
                             InboundRequestHistoryId historyId,
                             InboundRecord lastRecord,
                             LocalDateTime receivedTime) {

        // 构建新的入库记录对象
        InboundRecord record = inboundRecordConverter.transferOrderItemToInboundRecord(tsItem);
        record.setWarehouseType(WarehouseProviderType.FBA);
        record.setRequestHistoryId(historyId);

        Integer receivedQty = tsItem.getQty(); // 实际签收数量默认为发货数量
        Integer putawayQty = calculateReceivedQuantity(shipmentItem, tsItem); // 上架数量

        //签收
        boolean haveNewReceived = record.buildReceivedInfo(receivedQty, lastRecord);
        record.setReceivedTime(receivedTime);

        //上架
        boolean haveNewPutaway = record.buildPutawayInfo(putawayQty, lastRecord);
        record.setPutawayTime(receivedTime);
        record.setPutawayStatus(InboundStatus.FINISH);
        record.setStatus(InboundStatus.FINISH);

        // 记录上架完成时间
        tsItem.setReceivedEndTime(receivedTime);

        // 如果是首次签收，记录时间
        if (tsItem.checkIsFirstReceived()) {
            tsItem.setReceivedStartTime(receivedTime);
        }

        // 更新 TS Item 的签收和上架数量
        tsItem.setReceivedQty(receivedQty);
        tsItem.setPutawayQty(putawayQty);
        tsItem.calculateTheDifferenceValue(tsItem.getReceivedQty(), tsItem.getPutawayQty());
        // 如果本次存在新增的签收或上架记录，则落库并更新状态
        if (haveNewReceived || haveNewPutaway) {
            // 保存签收/上架记录
            inboundRecordRepository.add(record);
        }
    }

    /**
     * 计算签收数量
     */
    private int calculateReceivedQuantity(GetTransferOrderInfoResponse.Inbound shipmentItem, TransferOrderItem tsItem) {
        //签收数量：receiveNum * TS的装箱数量
        return Integer.parseInt(shipmentItem.getReceiveNum()) * tsItem.getProduct().getQuantityPerBox();
    }

    public void updateSyncApiStatus(String tsNo, SyncApiStatus syncApiStatus) {
        transferOrderRepository.updateSyncApiStatus(tsNo, syncApiStatus);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<String> submit(Collection<TransferOrder> domains) {
        domains = CollectionUtils.emptyIfNull(domains);
        List<String> instanceIds = new ArrayList<>(domains.size());
        for (TransferOrder domain : domains) {
            TransferOrderBpmForm form = new TransferOrderBpmForm(domain);
            ProcessInstanceId instanceId = processInstanceRepository.start(form);
            ProcessInstanceRecordVO instance = processInstanceRepository.getProcessInstance(instanceId.id());
            String instanceBizCode = Optional.ofNullable(instance).map(ProcessInstanceRecordVO::getProcessInstanceBizCode).orElse("");
            domain.setInstanceId(instanceId);
            domain.setInstanceBizCode(instanceBizCode);
            domain.setStatus(TransferOrderStatus.PENDING_APPROVAL);
            transferOrderRepository.updateById(domain);
            transferOrderStatusHistoryRepository.addHistoryStatus(domain.getId().id(), domain.getTsNo(), domain.getStatus().getValue());
            instanceIds.add(instanceId.id());
        }
        return instanceIds;
    }

    @Transactional(rollbackFor = Exception.class)
    public TransferOrder cancel(TransferOrder command) {
        // 根据状态判断是否可以取消
        // 1. 草稿、审批拒绝、待创建入库: TS单状态更新到已作废
        // 2. 待下发出库: 把该TS单状态更新到“已作废”状态；同时针对目的仓是API发起创建入库单的，则触发调取消入库单接口，取消入库单，成功则记录日志，失败的需要在异常提示：取消入库单失败，失败原因XXX【失败的需要重试】；【无API的手动线下去取消】
        // 3. 待发货: 校验出库单已经取消成功【区分API调用取消、手动去取消的（暂无判断）】,
        // 3.0. 判断是否走API: 仓库服务供应商为WMS/京东仓/极智佳/kingspark(kingspark逻辑为服务供应商为kingspark且调拨单的销售渠道是Amazon的并且目的仓是平台仓)
        // 3.1. 走API(同步): 点击确定同时触发调取消出库单接口，接口返回取消失败的则TS作废失败，在当前弹框提示“出库单取消失败，失败原因：XXX，TS单作废失败”“Outbound Order Cancellation Failed. Failure Reason: XXX. Transfer Order Void Failed.”；接口返回取消成功则TS作废成功及提示“出库单已取消，TS单作废成功”“Outbound Order Cancelled，Transfer Order Successfully Voided.”，关闭弹框，把该TS单状态更新到“已作废”状态，记录操作日志
        // 接口返回取消失败的则TS作废失败，在当前弹框提示“出库单取消失败，失败原因：XXX，TS单作废失败”“Outbound Order Cancellation Failed. Failure Reason: XXX. Transfer Order Void Failed.”；
        // 3.2. 无API: 手动线下去取消
        // 4. 针对TS单出库单取消成功的
        // 4.1. 走API: 也要调入库单取消接口，取消入库单【成功和失败不影响TS作废成功】，如果失败的，在异常提示取消入库单失败，失败原因XXX【失败的需要重试】
        // 4.2. 无API: 手动线下去取消
        // TODO 库存解锁
        inventoryRepository.unlock(command);
        return transferOrderRepository.cancel(command);
    }

    @Transactional(rollbackFor = Exception.class)
    public TransferOrder uploadInboundOrder(TransferOrder cmd) {

        transferOrderRepository.updateShipmentIdInfo(cmd.getShipmentId(), cmd.getReferenceId(), cmd.getId(), cmd.getTsNo());

        //文件存储
        List<OrderFile> orderFiles = new ArrayList<>();
        for (Integer fileId : cmd.getCartonLabelFileIds()) {
            OrderFileId orderFileId = new OrderFileId(fileId);
            OrderFile file = new OrderFile(orderFileId);
            file.setOrderNo(cmd.getTsNo());
            file.setOrderId(cmd.getId().id());
            file.setFileType(FileTypeEnum.CARTON_LABEL);
            file.setBusinessType(BusinessTypeEnum.TS);
            orderFiles.add(file);
        }
        //保存文件
        orderFileRepository.batchSave(orderFiles);

        return transferOrderLookup.findById(cmd.getId()).get();
    }


    @Transactional(rollbackFor = Exception.class)
    public void markRelabelFinish(TransferOrderItem cmd){

        transferOrderItemRepository.updateRelabelFinish(cmd);

        //文件存储
        List<OrderFile> orderFiles = new ArrayList<>();
        for (Integer fileId : cmd.getRelabelFinishFileIds()) {
            OrderFileId orderFileId = new OrderFileId(fileId);
            OrderFile file =  new OrderFile(orderFileId);
            file.setOrderNo(cmd.getTsNo());
            file.setOrderId(cmd.getId().id());
            file.setFileType(FileTypeEnum.CARTON_LABEL);
            file.setBusinessType(BusinessTypeEnum.TS);
            orderFiles.add(file);
        }
        //保存文件
        orderFileRepository.batchSave(orderFiles);
    }


    @Transactional(rollbackFor = Exception.class)
    public List<TransferOrder> relabelFinish(Collection<TransferOrder> commands) {
        return transferOrderRepository.relabelFinish(commands);
    }
}
