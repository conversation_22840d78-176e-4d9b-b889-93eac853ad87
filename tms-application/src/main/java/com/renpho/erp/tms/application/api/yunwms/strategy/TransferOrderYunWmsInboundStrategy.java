package com.renpho.erp.tms.application.api.yunwms.strategy;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.renpho.erp.apiproxy.eccang.yunwms.model.instock.*;
import com.renpho.erp.apiproxy.eccang.yunwms.model.transfer.*;
import com.renpho.erp.tms.application.discrepancy.DiscrepancyService;
import com.renpho.erp.tms.application.inbound.InboundRecordQueryService;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderService;
import com.renpho.erp.tms.application.transportrequest.PushTaskQueryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.domain.inbound.*;
import com.renpho.erp.tms.domain.orderfile.OrderFile;
import com.renpho.erp.tms.domain.orderfile.OrderFileRepository;
import com.renpho.erp.tms.domain.transferorder.*;
import com.renpho.erp.tms.domain.transportrequest.PushTask;
import com.renpho.erp.tms.domain.transportrequest.PushTaskStatus;
import com.renpho.erp.tms.domain.transportrequest.PushTaskType;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.erp.tms.infrastructure.common.constant.YunWmsConstant;
import com.renpho.erp.tms.infrastructure.common.converter.MultiLanguageConverter;
import com.renpho.erp.tms.infrastructure.persistence.inbound.po.converter.InboundRecordConverter;
import com.renpho.erp.tms.infrastructure.persistence.orderfile.po.converter.OrderFileConverter;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.repository.TransferOrderItemRepository;
import com.renpho.erp.tms.infrastructure.util.DateUtils;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * TransferOrder入库单生成策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransferOrderYunWmsInboundStrategy implements YunWmsInboundOrderStrategy<TransferOrder> {

    private final PushTaskService pushTaskService;
    private final PushTaskQueryService pushTaskQueryService;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransferOrderRepository transferOrderRepository;
    private final TransferOrderService transferOrderService;
    private final MultiLanguageConverter multiLanguageConverter;
    private final TransferOrderStatusHistoryRepository transferOrderStatusHistoryRepository;
    private final DiscrepancyService discrepancyService;

    private final TransferOrderItemRepository transferOrderItemRepository;
    private final InboundRecordQueryService inboundRecordQueryService;
    private final InboundRecordRepository inboundRecordRepository;
    private final InboundRecordConverter inboundRecordConverter;
    private final OrderFileRepository orderFileRepository;
    private final OrderFileConverter orderFileConverter;

    @Override
    public TransferOrderSaveRequest buildCreateTransferInboundRequest(TransferOrder order) {
        // TransferOrder的转运单创建逻辑
        TransferOrderSaveRequest request = new TransferOrderSaveRequest();
        request.setAddService(0);
        request.setTransferWay(1);
        request.setWarehouseNo(order.getDestWarehouse().getThirdWarehouseCode());
        request.setReferenceNo(order.getTsNo());

        request.setPredictArriveTime(DateUtil.format(order.getEstimatedDeliveryTime(), DatePattern.NORM_DATE_PATTERN));

        // 构建箱子信息和SKU信息
        List<TransferOrderSaveRequest.InboundBoxDTO> inboundBoxList = order.getItems().stream().map(item -> {
            TransferOrderSaveRequest.InboundBoxDTO inboundItem = new TransferOrderSaveRequest.InboundBoxDTO();
            inboundItem.setBoxMark(order.getTsNo());
            inboundItem.setHeight(item.getProduct().getActiveBoxSpec().getBoxHeightMetric().doubleValue());
            inboundItem.setWidth(item.getProduct().getActiveBoxSpec().getBoxWidthMetric().doubleValue());
            inboundItem.setLength(item.getProduct().getActiveBoxSpec().getBoxLengthMetric().doubleValue());
            inboundItem.setSkuDesc(item.getProduct().getPsku());
            inboundItem.setTotalBoxNum(item.getBoxQty().doubleValue());
            inboundItem.setWeight(item.getTotalGrossWeight().doubleValue());

            TransferOrderSaveRequest.InboundSkuDTO skuItem = new TransferOrderSaveRequest.InboundSkuDTO();
            skuItem.setProductCode(item.getProduct().getFnSku());
            String cnName = multiLanguageConverter.findCnName(item.getProduct().getNames());
            skuItem.setProductDesc(cnName);
            skuItem.setSingleNum(item.getQuantityPerBox());
            inboundItem.setSkuList(List.of(skuItem));

            return inboundItem;
        }).toList();

        request.setInboundList(inboundBoxList);
        return request;
    }

    @Override
    public TransferOrderLabelRenderBoxMarkRequest buildBoxLabelRequest(TransferOrder order) {
        TransferOrderLabelRenderBoxMarkRequest req = new TransferOrderLabelRenderBoxMarkRequest();
        req.setTransferNo(order.getShipmentId());
        req.setBoxMarkType(1);
        req.setTemplateId(14721);
        return req;
    }

    @Override
    public TransferOrderCancelRequest buildCancelTransferOrderRequest(TransferOrder order) {
        TransferOrderCancelRequest req = new TransferOrderCancelRequest();
        req.setBusinessNo(order.getShipmentId());
        return req;
    }

    @Override
    public GetTransferOrderInfoRequest buildGetTransferOrderInfoRequest(TransferOrder order) {
        GetTransferOrderInfoRequest req = new GetTransferOrderInfoRequest();
        req.setTransferNo(order.getShipmentId());
        return req;
    }

    @Override
    public CreateAsnRequest buildCreateAsnRequest(TransferOrder order) {
        CreateAsnRequest request = new CreateAsnRequest();
        request.setReference_no(order.getTsNo());
        request.setIncome_type(0);
        request.setReceiving_type("D");
        request.setWarehouse_code(order.getDestWarehouse().getThirdWarehouseCode());
        request.setReceiving_desc(order.getTsNo());
        if (order.getEstimatedDeliveryTime() != null) {
            Date etaDate = Date.from(order.getEstimatedDeliveryTime().atZone(ZoneId.of("UTC")).toInstant());
            request.setEta_date(etaDate);
        }
        request.setSpontaneous_head_cheng_type(2);
        request.setBulk_cargo_type_piece(1);
        request.setStock_type(0);

        List<CreateAsnRequest.ReceivingItemDTO> items = getReceivingItemDTOS(order);

        request.setItems(items);
        return request;
    }

    @Override
    public GetReceivingBoxPdfByCodeRequest buildGetAsnBoxLabelRequest(TransferOrder order) {
        GetReceivingBoxPdfByCodeRequest req = new GetReceivingBoxPdfByCodeRequest();
        req.setReceiving_code(order.getShipmentId());
        req.setPdf_type("box");
        req.setPdf_size("100*100-PT");
        req.setMade_in_china(1);
        req.setContent_type("url");
        return req;
    }


    @Override
    public CancelAsnRequest buildCancelAsnRequest(TransferOrder order) {
        CancelAsnRequest req = new CancelAsnRequest();
        req.setReceiving_code(order.getShipmentId());
        return req;
    }

    @Override
    public CetAsnListRequest buildGetAsnListRequest(TransferOrder order) {
        CetAsnListRequest req = new CetAsnListRequest();
        req.setReceiving_code(order.getShipmentId());
        return req;
    }

    @Override
    public PushTask createPushTask(TransferOrder order) {
        // 需要根据TransferOrder创建PushTask的逻辑
        return pushTaskService.createInbound(order, PushTaskStatus.PENDING, 0, null);
    }

    @Override
    public PushTask getCartonFileGenerationTask(TransferOrder order) {
        return pushTaskQueryService.findByBizAndType(order, List.of(PushTaskType.CARTON_FILE_GENERATION))
                .stream().findFirst()
                .orElseThrow(() -> new RuntimeException("找不到类型=CARTON_FILE_GENERATION的task数据, tsNo=" + order.getOrderNo()));
    }

    @Override
    public InboundRequestHistory addRequestHistory(TransferOrder order, Object request) {
        return inboundRequestHistoryService.add(order, WarehouseProviderType.POLARIS_YUNWMS, null, request);
    }

    @Override
    public InboundRequestHistory updateRequestHistory(InboundRequestHistoryId historyId, Integer responseCode,
                                                      Object responseHeaders, Object responseBody) {
        return inboundRequestHistoryService.update(historyId, responseCode, responseHeaders, responseBody);
    }

    @Override
    public void updateOrder(TransferOrder order, String transferNo) {
        order.setShipmentId(transferNo);
        order.setSyncApiStatus(SyncApiStatus.SUCCESS);
        transferOrderRepository.update(order);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCartonLabelFiles(TransferOrder order, List<String> fileIds) {
        for (String fileId : fileIds) {
            OrderFile orderFile = orderFileConverter.transferOrderToDomain(order, fileId);
            orderFileRepository.insert(orderFile);
        }
        order.setSyncApiStatus(SyncApiStatus.SUCCESS);
        order.setStatus(TransferOrderStatus.WAIT_CREATE_SHIPMENT);
        //todo 记录TS状态
        transferOrderRepository.update(order);
    }

    @Override
    public void sendCartonFilesMessage(TransferOrder order) {
        //不需要推送箱唛到FMS
    }

    @Override
    public String getOrderNo(TransferOrder order) {
        return order.getTsNo();
    }

    @Override
    public PushTask createCancelTask(TransferOrder order) {
        return pushTaskService.cancel(order, PushTaskStatus.PENDING, 0, null);
    }

    @Override
    public void clearShipmentId(TransferOrder order) {
        transferOrderService.clearShipmentIdById(order.getId());
    }

    @Override
    public PushTask createInboundPushTask(TransferOrder order) {
        return pushTaskService.inbound(order, PushTaskStatus.PENDING, 0, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleTransferOrderData(GetTransferOrderInfoResponse data, TransferOrder order, InboundRequestHistoryId historyId) {
        Integer status = Integer.parseInt(data.getStatus());
        if (!YunWmsConstant.TransferOrderStatus.COMPLETED.equals(status)) {
            return;
        }

        //将itemData按照FulfillmentNetworkSKU进行分组，返回一个Map，key为FulfillmentNetworkSKU，value为InboundShipmentItem
        Map<String, GetTransferOrderInfoResponse.Inbound> inboundDataMap = data.getInboundList().stream().collect(
                Collectors.toMap(
                        GetTransferOrderInfoResponse.Inbound::getSkuDesc,
                        Function.identity(),
                        (v, v2) -> v
                )
        );

        //获取签收时间
        String receivedTimeStr = data.getReceiveRecords().stream().findFirst()
                .map(GetTransferOrderInfoResponse.ReceiveRecords::getEcCreateTime)
                .orElse("0000-00-00 00:00:00");
        LocalDateTime receivedTime = DateUtils.toUtcLocalDateTime(receivedTimeStr);

        processReceiveRecord(inboundDataMap, receivedTime, order, historyId);
    }

    private void processReceiveRecord(Map<String, GetTransferOrderInfoResponse.Inbound> inboundDataMap,
                                      LocalDateTime receivedTime,
                                      TransferOrder ts,
                                      InboundRequestHistoryId historyId) {
        for (TransferOrderItem tsItem : ts.getItems()) {
            if (!inboundDataMap.containsKey(tsItem.getPsku())) {
                continue;
            }
            GetTransferOrderInfoResponse.Inbound shipmentItem = inboundDataMap.get(tsItem.getPsku());

            // 查询上一次记录，用于差异计算
            InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tsItem).orElse(null);

            processItem(shipmentItem, tsItem, historyId, lastRecord, receivedTime);
        }

        //处理完签收逻辑后，处理 TS 和 TS Item 的更新
        transferOrderItemRepository.batchUpdate(ts.getItems());

        //TS单状态记录
        transferOrderStatusHistoryRepository.addHistoryStatus(ts.getId().id(), ts.getTsNo(), TransferOrderStatus.RECEIVING.getValue());
        ts.setStatus(TransferOrderStatus.RECEIVED);
        transferOrderStatusHistoryRepository.addHistoryStatus(ts.getId().id(), ts.getTsNo(), TransferOrderStatus.RECEIVED.getValue());

        // 处理TS单的签收差异
        discrepancyService.handleTsDiscrepancy(ts);

        ts.setSyncApiStatus(SyncApiStatus.SUCCESS);
        //更新TS单
        transferOrderRepository.updateById(ts);

    }

    /**
     * 处理单个ITEM 的签收与上架逻辑，并记录入库信息。
     *
     * @param shipmentItem ShipmentItem
     * @param tsItem       TS Item
     * @param historyId    历史ID
     * @param lastRecord   上一次记录
     */
    private void processItem(GetTransferOrderInfoResponse.Inbound shipmentItem,
                             TransferOrderItem tsItem,
                             InboundRequestHistoryId historyId,
                             InboundRecord lastRecord,
                             LocalDateTime receivedTime) {

        // 构建新的入库记录对象
        InboundRecord record = inboundRecordConverter.transferOrderItemToInboundRecord(tsItem);
        record.setWarehouseType(WarehouseProviderType.POLARIS_YUNWMS);
        record.setRequestHistoryId(historyId);

        Integer receivedQty = tsItem.getQty(); // 实际签收数量默认为发货数量
        Integer putawayQty = calculateReceivedQuantity(shipmentItem, tsItem); // 上架数量

        //签收
        boolean haveNewReceived = record.buildReceivedInfo(receivedQty, lastRecord);
        record.setReceivedTime(receivedTime);

        //上架
        boolean haveNewPutaway = record.buildPutawayInfo(putawayQty, lastRecord);
        record.setPutawayTime(receivedTime);
        record.setPutawayStatus(InboundStatus.FINISH);
        record.setStatus(InboundStatus.FINISH);

        // 记录上架完成时间
        tsItem.setReceivedEndTime(receivedTime);

        // 如果是首次签收，记录时间
        if (tsItem.checkIsFirstReceived()) {
            tsItem.setReceivedStartTime(receivedTime);
        }

        // 更新 TS Item 的签收和上架数量
        tsItem.setReceivedQty(receivedQty);
        tsItem.setPutawayQty(putawayQty);
        tsItem.calculateTheDifferenceValue(tsItem.getReceivedQty(), tsItem.getPutawayQty());
        // 如果本次存在新增的签收或上架记录，则落库并更新状态
        if (haveNewReceived || haveNewPutaway) {
            // 保存签收/上架记录
            inboundRecordRepository.add(record);
        }
    }

    /**
     * 计算签收数量
     */
    private int calculateReceivedQuantity(GetTransferOrderInfoResponse.Inbound shipmentItem, TransferOrderItem tsItem) {
        //签收数量：receiveNum * TS的装箱数量
        return Integer.parseInt(shipmentItem.getReceiveNum()) * tsItem.getProduct().getQuantityPerBox();
    }

    @NotNull
    private static List<CreateAsnRequest.ReceivingItemDTO> getReceivingItemDTOS(TransferOrder order) {
        List<CreateAsnRequest.ReceivingItemDTO> items = new ArrayList<>();

        int currentBoxNo = 1, maxBoxNo = 0;
        for (TransferOrderItem item : order.getItems()) {
            int boxNos = item.getBoxQty().intValue();
            maxBoxNo += boxNos;

            for (int i = currentBoxNo; i <= maxBoxNo; i++) {
                CreateAsnRequest.ReceivingItemDTO itemDTO = new CreateAsnRequest.ReceivingItemDTO();
                itemDTO.setProduct_sku(item.getProduct().getFnSku());
                itemDTO.setQuantity(item.getQuantityPerBox());
                itemDTO.setBox_no(i);
                itemDTO.setInventory_type(0);
                items.add(itemDTO);
            }
            currentBoxNo += boxNos;
        }
        return items;
    }

    @Override
    public void updateAsnCartonLabelFiles(TransferOrder order, List<String> fileIds) {
        for (String fileId : fileIds) {
            OrderFile orderFile = orderFileConverter.transferOrderToDomain(order, fileId);
            orderFileRepository.insert(orderFile);
        }
        order.setSyncApiStatus(SyncApiStatus.SUCCESS);
        order.setStatus(TransferOrderStatus.WAIT_CREATE_SHIPMENT);
        transferOrderRepository.update(order);
    }

    @Override
    public PushTask createCancelAsnTask(TransferOrder order) {
        return pushTaskService.cancel(order, PushTaskStatus.PENDING, 0, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleAsnListData(List<GetAsnListResponse.AsnListData> dataList, TransferOrder order, InboundRequestHistoryId historyId) {
        dataList.forEach(asn -> processAsnListData(asn, order, historyId));
    }

    /**
     * 处理ASN列表数据
     */
    private void processAsnListData(GetAsnListResponse.AsnListData data, TransferOrder order, InboundRequestHistoryId historyId) {
        String receivingStatus = data.getReceiving_status();
        if (!isValidAsnReceivingStatus(receivingStatus)) {
            return;
        }

        Map<String, Integer> receivedQtyByFnsku = groupReceivedQtyByFnsku(data);

        LocalDateTime receivedTime = StringUtil.isNotBlank(data.getWarehouse_receiving_complete_time()) ?
                DateUtils.toUtcLocalDateTime(data.getWarehouse_receiving_complete_time()) : null;
        LocalDateTime shelfTime = StringUtil.isNotBlank(data.getWarehouse_shelf_time()) ?
                DateUtils.toUtcLocalDateTime(data.getWarehouse_shelf_time()) : null;

        processTransferOrderItems(order, receivedQtyByFnsku, receivedTime, shelfTime, historyId, receivingStatus);
        updateTransferOrderStatus(order, receivingStatus);
    }

    /**
     * 检查ASN签收状态是否有效
     */
    private boolean isValidAsnReceivingStatus(String receivingStatus) {
        return !Set.of(YunWmsConstant.InboundOrderStatus.RECEIVE_COMPLETED_DEST,
                YunWmsConstant.InboundOrderStatus.COMPLETED_PUTAWAY).contains(receivingStatus);
    }

    /**
     * 按FNSKU分组统计签收数量
     */
    private Map<String, Integer> groupReceivedQtyByFnsku(GetAsnListResponse.AsnListData data) {
        return data.getItems().stream()
                .collect(Collectors.groupingBy(
                        GetAsnListResponse.Item::getProduct_sku,
                        Collectors.summingInt(item -> Integer.parseInt(item.getReceived_quantity()))
                ));
    }

    /**
     * 处理调拨单项目
     */
    private void processTransferOrderItems(TransferOrder order,
                                           Map<String, Integer> receivedQtyByFnsku,
                                           LocalDateTime receivedTime,
                                           LocalDateTime shelfTime,
                                           InboundRequestHistoryId historyId,
                                           String receivingStatus) {
        for (TransferOrderItem tsItem : order.getItems()) {
            Integer receivedQty = receivedQtyByFnsku.get(tsItem.getFnSku());
            if (receivedQty == null) {
                continue;
            }

            InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tsItem).orElse(null);
            processAsnItem(receivedQty, tsItem, historyId, lastRecord, shelfTime, receivedTime, receivingStatus);
        }
    }

    /**
     * 更新调拨单状态
     */
    private void updateTransferOrderStatus(TransferOrder order, String receivingStatus) {
        transferOrderItemRepository.batchUpdate(order.getItems());
        if (YunWmsConstant.InboundOrderStatus.RECEIVE_COMPLETED_DEST.equals(receivingStatus) && order.getStatus() != TransferOrderStatus.RECEIVING) {
            order.setStatus(TransferOrderStatus.RECEIVING);
            transferOrderStatusHistoryRepository.addHistoryStatus(order.getId().id(), order.getTsNo(), TransferOrderStatus.RECEIVING.getValue());
        } else {
            order.setStatus(TransferOrderStatus.RECEIVED);
            transferOrderStatusHistoryRepository.addHistoryStatus(order.getId().id(), order.getTsNo(), TransferOrderStatus.RECEIVED.getValue());

            //TS单为已签收，判断是否有签收差异？如果有差异，则生成差异单
            // 处理TS单的签收差异
            discrepancyService.handleTsDiscrepancy(order);
        }
        order.setSyncApiStatus(SyncApiStatus.SUCCESS);
        transferOrderRepository.updateById(order);
    }

    /**
     * 处理单个ITEM 的签收与上架逻辑，并记录入库信息。
     *
     * @param tsItem     TS Item
     * @param historyId  历史ID
     * @param lastRecord 上一次记录
     */
    private void processAsnItem(Integer receivedQty,
                                TransferOrderItem tsItem,
                                InboundRequestHistoryId historyId,
                                InboundRecord lastRecord,
                                LocalDateTime shelfTime,
                                LocalDateTime receivedTime,
                                String receivingStatus) {

        // 构建新的入库记录对象
        InboundRecord record = inboundRecordConverter.transferOrderItemToInboundRecord(tsItem);
        record.setWarehouseType(WarehouseProviderType.POLARIS_YUNWMS);
        record.setRequestHistoryId(historyId);

        boolean isFirstReceived = tsItem.checkIsFirstReceived();

        //签收
        boolean haveNewReceived = record.buildReceivedInfo(receivedQty, lastRecord);
        record.setReceivedTime(receivedTime);

        //上架
        boolean haveNewPutaway = record.buildPutawayInfo(receivedQty, lastRecord);
        record.setPutawayTime(shelfTime);

        if (YunWmsConstant.InboundOrderStatus.COMPLETED_PUTAWAY.equals(receivingStatus)) {
            record.setPutawayStatus(InboundStatus.FINISH);
            record.setStatus(InboundStatus.FINISH);
        } else {
            record.setPutawayStatus(InboundStatus.WORKING);
            record.setStatus(InboundStatus.WORKING);
        }

        // 记录上架完成时间
        tsItem.setReceivedEndTime(receivedTime);
        tsItem.setPutawayStartTime(shelfTime);
        tsItem.setPutawayEndTime(shelfTime);

        // 如果是首次签收，记录时间
        if (isFirstReceived) {
            tsItem.setReceivedStartTime(receivedTime);
        }

        // 更新 TS Item 的签收和上架数量
        tsItem.setReceivedQty(receivedQty);
        tsItem.setPutawayQty(receivedQty);
        tsItem.calculateTheDifferenceValue(tsItem.getReceivedQty(), tsItem.getPutawayQty());
        // 如果本次存在新增的签收或上架记录，则落库并更新状态
        if (haveNewReceived || haveNewPutaway) {
            // 保存签收/上架记录
            inboundRecordRepository.add(record);
        }
    }
}
