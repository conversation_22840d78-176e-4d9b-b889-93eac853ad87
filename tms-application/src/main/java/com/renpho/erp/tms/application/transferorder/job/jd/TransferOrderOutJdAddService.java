package com.renpho.erp.tms.application.transferorder.job.jd;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.apiproxy.jd.fop.JdResponseExt;
import com.renpho.erp.apiproxy.jd.fop.ProxyRoute;

import com.renpho.erp.apiproxy.jd.fop.api.FopOutStockApi;
import com.renpho.erp.apiproxy.jd.fop.model.outstock.DeliveryOutstockRequest;
import com.renpho.erp.apiproxy.jd.fop.model.outstock.WaybillPackDTO;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderService;
import com.renpho.erp.tms.application.transferorder.job.common.TransferOrderCommonService;

import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transferorder.*;

import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.erp.tms.domain.transportrequest.dto.WarehouseData;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;

/**
 * TS-目的仓为JD-创建出库单.
 *
 * <AUTHOR>
 * @since 2025/8/28
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransferOrderOutJdAddService {

    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransferOrderCommonService transferOrderCommonService;
    private final TransferOrderService transferOrderService;

    private final TransferOrderRepository transferOrderRepository;
    private final FopOutStockApi fopOutStockApi;

    /**
     * TS-目的仓为JD-创建出库单任务.
     */
    @Lock4j(name = "transfer:request:jd:outbound:add:basic")
    @Transactional(rollbackFor = Exception.class)
    public Boolean createOutboundOrder(TransferOrder ts) {
        WarehouseData warehouseData = transferOrderCommonService.getJDWarehouse(ts.getDestWarehouse().getId().id());
        if (warehouseData == null) {
            log.error("TS-目的仓为JD的定时器任务异常, 无法获取客户编码, tsId={}, destWarehouseId={}", ts.getId(), ts.getDestWarehouse().getId().id());
            return false;
        }

        ProxyRoute proxyRoute = new ProxyRoute();
        proxyRoute.setCustomerCode(warehouseData.getCustomerCode());

        TransferOrderCustomer customer = ts.getCustomer();
        DeliveryOutstockRequest request = new DeliveryOutstockRequest();
        request.setCustomerBillCode(ts.getTsNo());  // ts单号，这个很重要，查询也需要用到它
        request.setOutstockType(20); // 20-销售出库
        request.setOrderType(1); // 1, B2C
        request.setScOrderNo(ts.getTsNo()); // 订单号，这个很重要，查询也需要用到它
        request.setScOrderCreateTime(ts.getCreated().getOperateTime().toInstant(ZoneOffset.ofHours(8)).toEpochMilli()); // 转换为毫秒级时间戳
        // 下单账号, 系统维护，这里不需要添加
        request.setReceiverName(ts.getDestAddress());
        request.setReceiverCountry(ts.getDestAddress());
        request.setReceiverProvince(ts.getDestAddress());
        request.setReceiverCity(ts.getDestAddress());
        request.setReceiverAddress(ts.getDestAddress());
        request.setReceiverAddress2(ts.getDestAddress());
        request.setReceiverMobile(customer.getCustomerPhone());
        request.setReceiverZipCode(ts.getDestAddress());
        request.setWarehouseCode(warehouseData.getWarehouseCode());

        // sku主数据
        List<DeliveryOutstockRequest.OutstockGoodsDto> goodsDtoList = new ArrayList<>();
        List<TransferOrderItem> items = ts.getItems();

        // 1、TS单的平台为Amazon的并且仓库是“CA-京东仓”，则outUnit字段传2，num传SKU的箱数； 2、其他的传 1-件 ，num传SKU数量；
        if (ts.getSalesChannel().getChannelName().equals("Amazon") && ts.getDestWarehouse().getCode().equals("JDCA001")) {
            for(int i=1;i<=items.size();i++){
                TransferOrderItem item = items.get(i-1);
                DeliveryOutstockRequest.OutstockGoodsDto goodsDto = new DeliveryOutstockRequest.OutstockGoodsDto();
                goodsDto.setSku(item.getFnSku());
                goodsDto.setNum(item.getBoxQty().intValue());  // 箱数
                goodsDto.setOutUnit(2);  // 2-箱
                goodsDto.setLineNumber(String.valueOf(i));  // 行号不能重复
                goodsDtoList.add(goodsDto);
            }
        } else{
            for(int i=1;i<=items.size();i++){
                TransferOrderItem item = items.get(i-1);
                DeliveryOutstockRequest.OutstockGoodsDto goodsDto = new DeliveryOutstockRequest.OutstockGoodsDto();
                goodsDto.setSku(item.getFnSku());
                goodsDto.setNum(item.getQty());  // 件数
                goodsDto.setOutUnit(1);  // 1-件
                goodsDto.setLineNumber(String.valueOf(i));  // 行号不能重复
                goodsDtoList.add(goodsDto);
            }
        }
        request.setGoodsDtoList(goodsDtoList);

        // TS单物流方式的物流服务代码
        List<DeliveryOutstockRequest.PerformanceSpDTO> performanceSpDTOList = new ArrayList<>();
        DeliveryOutstockRequest.PerformanceSpDTO performanceSpDTO = new DeliveryOutstockRequest.PerformanceSpDTO();
        performanceSpDTO.setCode(ts.getLogisticsMode().getCode());
        performanceSpDTOList.add(performanceSpDTO);
        request.setPerformanceSpDTOList(performanceSpDTOList);

        // TODO 其他参数等待产品补充文档

        log.info("TS-目的仓为JD创建出库单, 编码={}， 主体参数= {}", JSON.toJSONString(proxyRoute), JSON.toJSONString(request));
        R<JdResponseExt<String, List<WaybillPackDTO>>> result = fopOutStockApi.deliveryOutstock(proxyRoute, request);
        log.info("TS-目的仓为JD创建出库单, 响应={}", JSON.toJSONString(result));
        Integer resultCode = 200;
        Object body = null;
        if (result != null && result.getData()!=null && result.getData().getResponse()!=null) {
            resultCode = result.getData().getResponse().getCode();
            if(result.getData().getResponse().getContent().isSuccess()){
                body = result.getData().getResponse().getContent();
                String iaNo = result.getData().getResponse().getContent().getData();

                // 创建成功，保存 iaNo
                ts.setShipmentId(iaNo);
                ts.setSyncApiStatus(SyncApiStatus.SUCCESS);
                transferOrderRepository.updateById(ts);

                // 3. 生成箱唛推送任务(这个跟tr生命周期重复，所以注释掉)
                // transportRequestCommonService.createCartonFile(tr, warehouseType);

                // 记录请求历史
                inboundRequestHistoryService.add(ts, WarehouseProviderType.JD, null, request, resultCode, null, result);
            } else {
                // 记录请求历史
                inboundRequestHistoryService.add(ts, WarehouseProviderType.JD, null, request, resultCode, null, result);
                throw new DingTalkWarning("API创建出库单失败，原因：%s".formatted(result.getData().getResponse().getContent().getErrorMsg()));
            }
        } else {
            // 记录请求历史
            inboundRequestHistoryService.add(ts, WarehouseProviderType.JD, null, request, resultCode, null, result);
            throw new DingTalkWarning("API创建出库单失败，原因：%s".formatted("返回数据为空, tsId = "+ts.getId().id()));
        }
        return true;
    }

}
