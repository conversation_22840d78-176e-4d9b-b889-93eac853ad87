package com.renpho.erp.tms.application.transferorder.yunwms;

import com.renpho.erp.apiproxy.eccang.yunwms.model.outbound.*;
import com.renpho.erp.tms.application.api.yunwms.YunWmsInboundOrderContext;
import com.renpho.erp.tms.application.api.yunwms.strategy.TransferOrderYunWmsInboundStrategy;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderQueryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.file.FileId;
import com.renpho.erp.tms.domain.file.FileInfo;
import com.renpho.erp.tms.domain.inbound.InboundRequestHistory;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.orderfile.BusinessTypeEnum;
import com.renpho.erp.tms.domain.orderfile.FileTypeEnum;
import com.renpho.erp.tms.domain.orderfile.OrderFile;
import com.renpho.erp.tms.domain.orderfile.OrderFileLookup;
import com.renpho.erp.tms.domain.saleschannel.SalesChannelConstant;
import com.renpho.erp.tms.domain.transferorder.*;
import com.renpho.erp.tms.domain.transportrequest.PushTask;
import com.renpho.erp.tms.domain.transportrequest.PushTaskStatus;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.erp.tms.domain.warehouse.Warehouse;
import com.renpho.erp.tms.domain.warehouse.WarehouseConfig;
import com.renpho.erp.tms.domain.warehouse.WarehouseId;
import com.renpho.erp.tms.infrastructure.common.constant.CommonConstant;
import com.renpho.erp.tms.infrastructure.common.constant.YunWmsConstant;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.repository.TransferOrderItemRepository;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.outbound.EccangOutBoundClient;
import com.renpho.erp.tms.infrastructure.remote.file.converter.FileConverter;
import com.renpho.erp.tms.infrastructure.remote.file.repository.FileLookup;
import com.renpho.erp.tms.infrastructure.remote.warehouse.repository.WarehouseLookup;
import com.renpho.erp.tms.infrastructure.util.DateUtils;
import com.renpho.karma.dto.R;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * TS单云仓入库服务
 *
 * <AUTHOR>
 * @since 2025/8/23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransferOrderYunWmsService {

    private final YunWmsInboundOrderContext yunWmsInboundOrderContext;
    private final TransferOrderYunWmsInboundStrategy transferOrderStrategy;
    private final TransferOrderQueryService transferOrderQueryService;
    private final EccangOutBoundClient eccangOutBoundClient;
    private final WarehouseLookup warehouseLookup;
    private final PushTaskService pushTaskService;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransferOrderRepository transferOrderRepository;
    private final TransferOrderItemRepository transferOrderItemRepository;
    private final FileLookup fileLookup;
    private final OrderFileLookup orderFileLookup;
    private final FileConverter fileConverter;

    /**
     * 为调拨单创建转运单
     */
    public void createTransferInboundForTs(List<String> tsNos, List<TransferOrderId> tsIds, Boolean isAmazon,
                                           String syncApiStatus) {

        //“待创建入库单”状态的TS单
        List<TransferOrder> tsList = transferOrderQueryService.findByWarehouseProviderType(
                WarehouseProviderType.POLARIS_YUNWMS,
                tsIds,
                tsNos,
                List.of(TransferOrderStatus.WAIT_CREATE_INBOUND),
                syncApiStatus
        ).stream().filter(ts -> StringUtil.isEmpty(ts.getShipmentId())).toList();

        if (isAmazon) {
            //amazon tr单，走中转逻辑
            List<TransferOrder> amzTsList = tsList.stream()
                    .filter(ts -> StringUtil.isEmpty(ts.getShipmentId())
                            && SalesChannelConstant.AMAZON.equalsIgnoreCase(ts.getSalesChannel().getChannelName()))
                    .toList();
            //创建 转运单（中转，amazon平台TS）
            yunWmsInboundOrderContext.executeCreateTransferInbound(amzTsList, transferOrderStrategy, TransferOrder::getDestWarehouse);
        } else {
            //非Amazon tr单，走一件代发逻辑
            List<TransferOrder> nonAmzTrs = tsList.stream()
                    .filter(ts -> StringUtil.isEmpty(ts.getShipmentId())
                            && !SalesChannelConstant.AMAZON.equalsIgnoreCase(ts.getSalesChannel().getChannelName()))
                    .toList();
            //创建 ASN（一件代发，非amazon平台TS）
            yunWmsInboundOrderContext.executeCreateAsn(nonAmzTrs, transferOrderStrategy, TransferOrder::getDestWarehouse);
        }
    }

    /**
     * 获取走极智佳中转的调拨单箱唛
     *
     * @param tsNos         调拨单号列表
     * @param ids           调拨单ID列表
     * @param syncApiStatus 同步状态
     */
    public void getTransferOrderBoxLabelForTs(List<String> tsNos, List<TransferOrderId> ids, String syncApiStatus) {
        //“待创建入库单”状态的TS单
        List<TransferOrder> tsList = transferOrderQueryService.findByWarehouseProviderType(
                WarehouseProviderType.POLARIS_YUNWMS,
                ids,
                tsNos,
                List.of(TransferOrderStatus.WAIT_CREATE_INBOUND),
                syncApiStatus
        ).stream().filter(ts -> SalesChannelConstant.AMAZON.equalsIgnoreCase(ts.getSalesChannel().getChannelName())
                && StringUtil.isEmpty(ts.getShipmentId())).toList();

        yunWmsInboundOrderContext.executeGetTransferOrderBoxLabel(tsList, transferOrderStrategy, TransferOrder::getDestWarehouse);
    }


    /**
     * 获取走极智佳中转的调拨单入库信息
     *
     * @param tsNos         调拨单号列表
     * @param ids           调拨单ID列表
     * @param syncApiStatus 同步状态
     */
    public void pullTransferOrderForTS(List<String> tsNos, List<TransferOrderId> ids, String syncApiStatus) {
        //获取所有状态为已发货或部分签收的极智佳 TS单
        List<TransferOrder> tsList = transferOrderQueryService.findByWarehouseProviderType(
                WarehouseProviderType.POLARIS_YUNWMS,
                ids,
                tsNos,
                List.of(TransferOrderStatus.SHIPPED, TransferOrderStatus.RECEIVING),
                syncApiStatus
        ).stream().filter(ts -> SalesChannelConstant.AMAZON.equalsIgnoreCase(ts.getSalesChannel().getChannelName())
                && StringUtil.isNotBlank(ts.getShipmentId())).toList();

        yunWmsInboundOrderContext.executePullTransferOrder(tsList, transferOrderStrategy, TransferOrder::getDestWarehouse);
    }

    /**
     * 取消(走极智佳中转的调拨单的)转运单
     *
     * @param tsNos         调拨单号列表
     * @param ids           调拨单ID列表
     * @param syncApiStatus 同步状态
     */
    public void cancelTransferOrderForTS(List<String> tsNos, List<TransferOrderId> ids, String syncApiStatus) {
        //获取所有状态为已作废的极智佳 TS单
        List<TransferOrder> tsList = transferOrderQueryService.findByWarehouseProviderType(
                WarehouseProviderType.POLARIS_YUNWMS,
                ids,
                tsNos,
                List.of(TransferOrderStatus.CANCEL),
                syncApiStatus
        ).stream().filter(ts -> SalesChannelConstant.AMAZON.equalsIgnoreCase(ts.getSalesChannel().getChannelName())
                && StringUtil.isNotBlank(ts.getShipmentId())).toList();


        yunWmsInboundOrderContext.executeCancelTransferOrder(tsList, transferOrderStrategy, TransferOrder::getDestWarehouse);
    }

    /**
     * 为非Amazon调拨单获取ASN箱唛（一件代发）
     */
    public void syncAsnBoxLabelForTs(List<String> tsNos, List<TransferOrderId> tsIds, String syncApiStatus) {
        //"待创建入库单"状态的TS单
        List<TransferOrder> tsList = transferOrderQueryService.findByWarehouseProviderType(
                WarehouseProviderType.POLARIS_YUNWMS,
                tsIds,
                tsNos,
                List.of(TransferOrderStatus.WAIT_CREATE_INBOUND),
                syncApiStatus
        ).stream().filter(ts -> !SalesChannelConstant.AMAZON.equalsIgnoreCase(ts.getSalesChannel().getChannelName())
                && StringUtil.isNotBlank(ts.getShipmentId())).toList();

        //获取 ASN箱唛（一件代发，非amazon平台TS）
        yunWmsInboundOrderContext.executeGetAsnBoxLabel(tsList, transferOrderStrategy, TransferOrder::getDestWarehouse);
    }

    /**
     * 为非Amazon调拨单取消ASN（一件代发）
     */
    public void cancelAsnForTs(List<String> tsNos, List<TransferOrderId> tsIds, String syncApiStatus) {
        //"待创建入库单"状态的TS单
        List<TransferOrder> tsList = transferOrderQueryService.findByWarehouseProviderType(
                WarehouseProviderType.POLARIS_YUNWMS,
                tsIds,
                tsNos,
                List.of(TransferOrderStatus.CANCEL),
                syncApiStatus
        ).stream().filter(ts -> !SalesChannelConstant.AMAZON.equalsIgnoreCase(ts.getSalesChannel().getChannelName())
                && StringUtil.isNotBlank(ts.getShipmentId())).toList();

        //取消 ASN（一件代发，非amazon平台TS）
        yunWmsInboundOrderContext.executeCancelAsn(tsList, transferOrderStrategy, TransferOrder::getDestWarehouse);
    }

    /**
     * 为非Amazon调拨单拉取ASN列表（一件代发）
     */
    public void pullAsnInfoForTs(List<String> tsNos, List<TransferOrderId> tsIds, String syncApiStatus) {
        //获取所有状态为已发货或部分签收的极智佳 TS单
        List<TransferOrder> tsList = transferOrderQueryService.findByWarehouseProviderType(
                WarehouseProviderType.POLARIS_YUNWMS,
                tsIds,
                tsNos,
                List.of(TransferOrderStatus.SHIPPED, TransferOrderStatus.RECEIVING),
                syncApiStatus
        ).stream().filter(ts -> !SalesChannelConstant.AMAZON.equalsIgnoreCase(ts.getSalesChannel().getChannelName())
                && StringUtil.isNotBlank(ts.getShipmentId())).toList();

        //拉取 ASN列表（一件代发，非amazon平台TS）
        yunWmsInboundOrderContext.executePullAsnList(tsList, transferOrderStrategy, TransferOrder::getDestWarehouse);
    }


    /**
     * 创建出库单
     *
     * @param tsList 调拨单集合
     * @return 出库单结果
     */
    public Map<TransferOrderId, YunWmsOutboundResultDTO> createOutbound(List<TransferOrder> tsList) {
        if (CollectionUtils.isEmpty(tsList)) {
            return Map.of();
        }

        List<WarehouseId> destinationIds = tsList.stream()
                .map(TransferOrder::getDestWarehouse)
                .map(Warehouse::getId)
                .toList();
        //获取调拨单目的仓仓库配置信息
        Map<WarehouseId, WarehouseConfig> warehouseConfigMap = warehouseLookup.getWarehouseConfigMap(destinationIds);

        Map<TransferOrderId, YunWmsOutboundResultDTO> resMap = new HashMap<>();
        for (TransferOrder ts : tsList) {
            String cartonFileUrl = getCartonFileUrl(ts);
            if (cartonFileUrl == null) {
                log.warn("{} 调拨单箱唛文件不存在。", ts.getTsNo());
                continue;
            }

            //创建出库单入参
            OutboundCreateRequest req = buildOutboundCreateRequest(ts, cartonFileUrl);

            WarehouseConfig warehouseConfig = warehouseConfigMap.get(ts.getDestWarehouseId());
            //调用API创建出库单
            R<OutboundCreateResponse> r = eccangOutBoundClient.createOutbound(warehouseConfig.getCustomerCode(), req);
            YunWmsOutboundResultDTO dto = new YunWmsOutboundResultDTO();
            if (r.isSuccess()) {
                OutboundCreateResponse response = r.getData();
                dto.setOutboundNo(response.getOutboundNo());
                ts.fillOutboundNo(response.getOutboundNo());
                //保存item的出库单号
                transferOrderItemRepository.batchUpdate(ts.getItems());
            } else {
                dto.setErrMsg(r.getMessage());
            }
            dto.setIsSuccess(r.isSuccess());
            resMap.put(ts.getId(), dto);
        }


        return resMap;
    }


    /**
     * 获取箱唛文件
     * @param ts 调拨单
     * @return 箱唛文件url
     */
    private String getCartonFileUrl(TransferOrder ts){
        //极智佳箱唛只有一个文件，所以只取第一个即可
        List<OrderFile> orderFiles = orderFileLookup.findListByOrderId(ts.getId().id(), BusinessTypeEnum.TS, FileTypeEnum.CARTON_LABEL);
        Optional<FileId> fileIdOpt = orderFiles.stream().findFirst().map(OrderFile::getFileInfo).map(FileInfo::getId);
        if (fileIdOpt.isEmpty()) {
            return null;
        }

        FileId fileId = fileIdOpt.get();
        Optional<FileInfo> fileR = fileLookup.findById(fileId);

        return fileR.map(FileInfo::getUrl).orElse(null);
    }


    /**
     * 取消极智佳平台出库单
     *
     * @param ts 调拨单
     * @return 是否取消成功 true-成功 false-失败
     */
    public boolean cancelOutbound(TransferOrder ts) {
        Optional<WarehouseId> warehouseIdOpt = Optional.of(ts)
                .map(TransferOrder::getDestWarehouse)
                .map(Warehouse::getId);
        if (warehouseIdOpt.isEmpty()) {
            throw new BusinessException("CANCEL_TS_OUTBOUND_ORDER_FAIL", "目的仓库不存在");
        }
        WarehouseConfig warehouseConfig = warehouseLookup.getWarehouseConfig(warehouseIdOpt.get());

        String outboundNo = ts.getItems().stream().findFirst().map(TransferOrderItem::getOutboundNo).orElse("");

        OutboundCancelRequest req = buildOutboundCancelRequest(outboundNo);
        InboundRequestHistory history = inboundRequestHistoryService.add(ts, WarehouseProviderType.POLARIS_YUNWMS, null, req);
        PushTask cancelTask = pushTaskService.cancelOutbound(ts, PushTaskStatus.PENDING, 0, null);
        try {
            R<String> ret = pushTaskService.execute(() -> {
                // 调API代理中心接口，取消出库单
                R<String> r = eccangOutBoundClient.cancelOutbound(warehouseConfig.getCustomerCode(), req);
                if (!r.isSuccess()) {
                    log.error("调用【易仓-取消出库单】接口异常：, tsNo={}，异常信息：{}", ts.getTsNo(), r);
                    inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                    //将异常抛出才能触发重试机制
                    throw new RuntimeException("API取消出库单失败，原因：%s".formatted(r.getMessage()));
                }
                return r;
            }, cancelTask);
            inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null, ret.getData());

        } catch (Exception e) {
            log.error("作废【极智佳出库单】失败, tsNo={}", ts.getTsNo(), e);
            throw new BusinessException("CANCEL_TS_OUTBOUND_ORDER_FAIL");
        }

        return true;
    }


    /**
     * 获取出库单详情，并更新调拨单出库信息
     * @param tsList 调拨单集合
     */
    public void getOutboundDetailAndThenUpdateTS(List<TransferOrder> tsList) {
        if (CollectionUtils.isEmpty(tsList)) {
            return ;
        }

        List<WarehouseId> destinationIds = tsList.stream()
                .map(TransferOrder::getDestWarehouse)
                .map(Warehouse::getId)
                .toList();
        Map<WarehouseId, WarehouseConfig> warehouseConfigMap = warehouseLookup.getWarehouseConfigMap(destinationIds);

        for (TransferOrder ts : tsList) {
            WarehouseConfig warehouseConfig = warehouseConfigMap.get(ts.getDestWarehouseId());

            String outboundNo = ts.getItems().stream().findFirst().map(TransferOrderItem::getOutboundNo).orElse("");

            OutboundDetailRequest req = buildOutboundDetailRequest(outboundNo);
            InboundRequestHistory history = inboundRequestHistoryService.add(ts, WarehouseProviderType.POLARIS_YUNWMS, null, req);
            PushTask outboundTask = pushTaskService.outbound(ts, PushTaskStatus.PENDING, 0, null);
            try {
                R<OutboundDetailResponse> ret = pushTaskService.execute(() -> {
                    // 调API代理中心接口，查询出库单
                    R<OutboundDetailResponse> r = eccangOutBoundClient.getOutboundDetail(warehouseConfig.getCustomerCode(), req);
                    if (!r.isSuccess()) {
                        log.error("调用【易仓-查询出库单】接口异常：, tsNo={}，异常信息：{}", ts.getTsNo(), r);
                        inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, r);
                        //将异常抛出才能触发重试机制
                        throw new RuntimeException("API查询出库单失败，原因：%s".formatted(r.getMessage()));
                    }
                    return r;
                }, outboundTask);
                inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null, ret.getData());


                String status = ret.getData().getStatus();
                for (TransferOrderItem item : ts.getItems()) {
                    item.setOutboundStatus(Integer.parseInt(status));
                    item.setOutboundTime(DateUtils.toUtcLocalDateTime(ret.getData().getOutboundTime()));
                }
                if (YunWmsConstant.OutboundStatus.SHIPPED.equals(status)) {
                    ts.setStatus(TransferOrderStatus.SHIPPED);
                    transferOrderRepository.updateById(ts);
                }
                ts.setSyncApiStatus(SyncApiStatus.SUCCESS);

                transferOrderItemRepository.batchUpdate(ts.getItems());

            } catch (Exception e) {
                log.error("查询【极智佳出库单】失败, tsNo={}", ts.getTsNo(), e);
            }
        }
    }

    /**
     * 构建查询出库单请求
     * @param outboundNo 出库单号
     * @return 请求
     */
    private OutboundDetailRequest buildOutboundDetailRequest(String outboundNo) {
        OutboundDetailRequest req = new OutboundDetailRequest();
        req.setOutboundNo(outboundNo);

        return req;
    }

    /**
     * 构建取消出库单请求
     *
     * @param outboundNo 出库单号
     * @return 请求
     */
    private OutboundCancelRequest buildOutboundCancelRequest(String outboundNo) {
        OutboundCancelRequest req = new OutboundCancelRequest();
        req.setBusinessNo(outboundNo);
        req.setNullifiedReason("取消发货");

        return req;
    }


    /**
     * 组装创建出库单请求
     *
     * @param ts 调拨单
     * @return 请求
     */
    private OutboundCreateRequest buildOutboundCreateRequest(TransferOrder ts, String cartonFileUrl) {
        OutboundCreateRequest req = new OutboundCreateRequest();
        req.setBoxMark(cartonFileUrl);

        req.setReferenceNo(ts.getTsNo());
        req.setFbaBoxType(ts.getShipmentId());
        // 根据业务类型判断 1、VC-DO的传VC 2、B2C补货并且销售渠道是Amazon的传FBA 3、其他的传other
        req.setBusinessType(ts.getOutboundBusinessType());
        req.setDestinationType(ts.getDestWarehouse().getType() == 1 ? 1 : 2);
        req.setFbaWarehouseName(ts.getDestWarehouse().getName());
        //todo 物流方式表的物流服务代码
        req.setSmCode(ts.getLogisticsMode().getCode());
        req.setNeedPalletLabel(0);
        req.setReferenceId(ts.getReferenceId());
        req.setShippmentId(ts.getShipmentId());
        req.setWarehouseNo(ts.getShippingWarehouseCode());


        List<OutboundCreateRequest.TransferBox> transferBoxes = new ArrayList<>();
        for (TransferOrderItem item : ts.getItems()) {
            OutboundCreateRequest.TransferBox transferBox = new OutboundCreateRequest.TransferBox();
            transferBox.setBoxMark(item.getFnSku());
            transferBox.setBoxNum(item.getBoxQty().intValue());
            transferBox.setChangeLabel("2");
            transferBox.setSkuDesc(item.getPsku());
            transferBoxes.add(transferBox);
        }

        req.setTransferBoxes(transferBoxes);
        return req;
    }
}
