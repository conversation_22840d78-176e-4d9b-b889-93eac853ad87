package com.renpho.erp.tms.infrastructure.persistence.transferorder.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.tms.domain.transferorder.TransferOrderStatusHistoryRepository;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.mapper.TransferOrderStatusHistoryMapper;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderStatusHistoryPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2025/6/9
 */
@Service
@RequiredArgsConstructor
public class TransferOrderStatusHistoryRepositoryImpl extends ServiceImpl<TransferOrderStatusHistoryMapper, TransferOrderStatusHistoryPO> implements TransferOrderStatusHistoryRepository {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addHistoryStatus(Integer tsId, String tsNo, Integer status) {
        TransferOrderStatusHistoryPO po = new TransferOrderStatusHistoryPO(tsId, tsNo, status);
        baseMapper.insert(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addHistoryStatus(Integer tsId, String tsNo, Integer status, Integer updateBy) {
        TransferOrderStatusHistoryPO po = new TransferOrderStatusHistoryPO(tsId, tsNo, status);
        po.setUpdateBy(updateBy);
        po.setCreateBy(updateBy);
        baseMapper.insert(po);
    }
}
