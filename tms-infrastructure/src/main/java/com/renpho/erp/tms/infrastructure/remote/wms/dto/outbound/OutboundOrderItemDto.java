package com.renpho.erp.tms.infrastructure.remote.wms.dto.outbound;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 调拨出库单明细表 (wms_transfer_outbound_order_item).
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Data
public class OutboundOrderItemDto {

    /**
     * PSKU
     */
    @NotNull(message = "{TRANSFER_SKU_NOT_NULL}")
    private String psku;

    /**
     * 产品条码
     */
    @NotNull(message = "{TRANSFER_BARCODE_NOT_NULL}")
    private String barcode;

    /**
     * 出库条码
     */
    @NotNull(message = "{TRANSFER_OUT_BARCODE_NOT_NULL}")
    private String outboundBarcode;

    /**
     * 货主ID
     * 1、不借货的，给TS的货主
     * 2、借货的，给出借方货主
     */
    private Integer ownerId;

    /**
     * 计划数量
     */
    @NotNull(message = "{TRANSFER_PLAN_QTY_NOT_NULL}")
    private Integer planQty;

    /**
     * 库存属性  0：不良品  1：良品
     */
    @NotNull(message = "{TRANSFER_ATTRIBUTES_NOT_NULL}")
    private Integer attributes;

    /**
     * 锁定数量,跟计划数量一致
     */
    //private Integer lockQty;

    /**
     * 是否被借调 0：否  1：是
      */
    private Integer isBorrowed;

    /**
     * 是否换标 0：否  1：是
     */
    private Integer isRelabel;

    /**
     * 借方PSKU
     * 1、不借货的不传
     * 2、借货的，给PSKU
     */
    private String borrowedPsku;

    /**
     * 借方出库barcode
     * 1、不借货的不传
     * 2、借货的，给FNSKU
     */
    private String borrowedBarcode;

    /**
     * 借方货主Id
     * 1、不借货的不传
     * 2、借货的，给TS的货主
     */
    private Integer borrowedOwnerId;

    /**
     * 产品标签链接
     * 1、换标的给，其他的不用给
     */
    private String productLabelUrl;

}

