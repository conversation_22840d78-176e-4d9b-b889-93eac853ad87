package com.renpho.erp.tms.infrastructure.persistence.transferorder.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 调拨单商品表 PO
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Getter
@Setter
@TableName(value = "tms_transfer_order_item", autoResultMap = true)
public class TransferOrderItemPO extends DefaultPO {

    @Serial
    private static final long serialVersionUID = 4252238474030205393L;

    /**
     * 调拨单ID
     */
    @TableField(value = "ts_id")
    private Integer tsId;

    /**
     * 调拨单号
     */
    @TableField(value = "ts_no")
    private String tsNo;

    /**
     * 产品ID
     */
    @TableField(value = "product_id")
    private Integer productId;

    /**
     * PSKU
     */
    @TableField(value = "psku")
    private String psku;

    /**
     * FNSKU
     */
    @TableField(value = "fnsku")
    private String fnsku;

    /**
     * 销售sku
     */
    @TableField(value = "seller_sku")
    private String sellerSku;

    /**
     * 产品图片
     */
    @TableField(value = "picture")
    private String picture;

    /**
     * PSKU版本
     */
    @TableField(value = "product_version")
    private String productVersion;


    /**
     * 销售渠道ID
     */
    @TableField(value = "sales_channel_id")
    private Integer salesChannelId;

    /**
     * 店铺ID
     */
    @TableField(value = "store_id")
    private Integer storeId;

    /**
     * 货主ID
     */
    @TableField(value = "owner_id")
    private Integer ownerId;

    /**
     * 差异单号
     */
    @TableField(value = "discrepancy_no")
    private String discrepancyNo;

    /**
     * 出库单号
     */
    @TableField(value = "outbound_no")
    private String outboundNo;


    /**
     * 出库状态: 0待出库， 1 已出库
     */
    @TableField(value = "outbound_status")
    private Integer outboundStatus;

    /**
     * 出库时间
     */
    @TableField(value = "outbound_time")
    private LocalDateTime outboundTime;

    /**
     * 发货数量
     */
    @TableField(value = "qty")
    private Integer qty;

    /**
     * 箱数
     */
    @TableField(value = "box_qty")
    private BigDecimal boxQty;

    /**
     * 总毛重
     */
    @TableField(value = "total_gross_weight")
    private BigDecimal totalGrossWeight;

    /**
     * 总体积
     */
    @TableField(value = "total_volume")
    private BigDecimal totalVolume;

    /**
     * 是否借货
     */
    @TableField(value = "is_borrowed")
    private Boolean isBorrowed;

    /**
     * 是否换标
     */
    @TableField(value = "is_relabel")
    private Boolean isRelabel;

    /**
     * 是否换标完成
     */
    @TableField(value = "is_relabel_finish")
    private Boolean isRelabelFinish;

    /**
     * 换标完成时间
     */
    @TableField(value = "relabel_finish_time")
    private LocalDateTime relabelFinishTime;

    /**
     * 销售金额
     */
    @TableField(value = "sale_amount")
    private BigDecimal saleAmount;

    /**
     * ASN标签文件ID, 仅 VC 类型有此文件, 展示时合并入箱唛文件
     */
    @TableField(value = "asn_label_file_id", typeHandler = FastjsonTypeHandler.class)
    private List<String> asnLabelFileIds;

    /**
     * ASN条码号
     */
    @TableField(value = "asn")
    private String asn;


    /**
     * 出借方PSKU
     */
    @TableField(value = "borrowed_psku")
    private String borrowedPsku;

    /**
     * 出借方FNSKU
     */
    @TableField(value = "borrowed_fnsku")
    private String borrowedFnsku;

    /**
     * 新产品标签文件ID
     */
    @TableField(value = "new_product_label_file_id")
    private String newProductLabelFileId;

    /**
     * 条码文件ID
     */
    @TableField(value = "barcode_file_id", typeHandler = FastjsonTypeHandler.class)
    private List<String> barcodeFileIds;

    /**
     * 出借方店铺ID
     */
    @TableField(value = "borrowed_store_id")
    private Integer borrowedStoreId;

    /**
     * 出借方货主ID
     */
    @TableField(value = "borrowed_owner_id")
    private Integer borrowedOwnerId;

    /**
     * 发货时间
     */
    @TableField(value = "departure_time")
    private LocalDateTime departureTime;

    /**
     * 发货要求
     */
    @TableField(value = "delivery_requirement")
    private String deliveryRequirement;

    /**
     * 是否打托, 0-否, 1-是
     */
    @TableField(value = "is_palletized")
    private Boolean isPalletized;

    /**
     * 签收数量
     */
    @TableField(value = "received_qty")
    private Integer receivedQty;

    /**
     * 签收差异
     */
    @TableField(value = "received_discrepancy")
    private Integer receivedDiscrepancy;

    /**
     * 签收开始时间
     */
    @TableField(value = "received_start_time")
    private LocalDateTime receivedStartTime;

    /**
     * 签收结束时间
     */
    @TableField(value = "received_end_time")
    private LocalDateTime receivedEndTime;

    /**
     * 上架数量
     */
    @TableField(value = "putaway_qty")
    private Integer putawayQty;

    /**
     * 上架差异
     */
    @TableField(value = "putaway_discrepancy")
    private Integer putawayDiscrepancy;

    /**
     * 上架开始时间
     */
    @TableField(value = "putaway_start_time")
    private LocalDateTime putawayStartTime;

    /**
     * 上架结束时间
     */
    @TableField(value = "putaway_end_time")
    private LocalDateTime putawayEndTime;

    /**
     * 尺寸单位
     */
    @TableField(value = "dimension_unit")
    private String dimensionUnit;

    /**
     * 外箱尺寸-长
     */
    @TableField(value = "box_length")
    private BigDecimal boxLength;

    /**
     * 中英文品名
     */
    @TableField(value = "product_name_cn")
    private String productNameCn;

    /**
     * 中英文品名
     */
    @TableField(value = "product_name_en")
    private String productNameEn;

    /**
     * 外箱尺寸-宽
     */
    @TableField(value = "box_width")
    private BigDecimal boxWidth;

    /**
     * 外箱尺寸-高
     */
    @TableField(value = "box_height")
    private BigDecimal boxHeight;

    /**
     * 重量单位
     */
    @TableField(value = "weight_unit")
    private String weightUnit;

    /**
     * 单品净重
     */
    @TableField(value = "weight")
    private BigDecimal weight;

    /**
     * 单品毛重
     */
    @TableField(value = "gross_weight")
    private BigDecimal grossWeight;

    /**
     * 整箱毛重
     */
    @TableField(value = "box_gross_weight")
    private BigDecimal boxGrossWeight;

    /**
     * 装箱数量
     */
    @TableField(value = "quantity_per_box")
    private Integer quantityPerBox;
}
