package com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.renpho.erp.ims.client.feign.inventory.command.InventoryDiscrepancyAddCommand;
import com.renpho.erp.ims.client.feign.inventory.dto.InventoryDiscrepancyDetailDto;
import com.renpho.erp.tms.domain.transferorder.TransferOrder;
import com.renpho.erp.tms.domain.transferorder.TransferOrderId;
import com.renpho.erp.tms.domain.transferorder.TransferOrderItem;
import com.renpho.erp.tms.infrastructure.common.converter.BooleanConverter;
import com.renpho.erp.tms.infrastructure.common.converter.TransferOrderStatusConverter;
import com.renpho.erp.tms.infrastructure.persistence.logisticsmode.po.converter.LogisticsModeConverter;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderPO;
import com.renpho.erp.tms.infrastructure.remote.owner.converter.OwnerConverter;
import com.renpho.erp.tms.infrastructure.remote.saleschannel.converter.SalesChannelConverter;
import com.renpho.erp.tms.infrastructure.remote.store.converter.StoreConverter;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import com.renpho.erp.tms.infrastructure.remote.warehouse.converter.WarehouseConverter;
import com.renpho.karma.dto.Paging;
import org.mapstruct.*;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 调拨单转换器
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {OperatorConverter.class,
                TransferOrderItemConverter.class,
                TransferOrderCustomerConverter.class,
                SalesChannelConverter.class,
                StoreConverter.class,
                OwnerConverter.class,
                WarehouseConverter.class,
                BooleanConverter.class,
                TransferOrderStatusConverter.class,
                LogisticsModeConverter.class
        })
public interface TransferOrderConverter {

    @Mapping(target = "salesChannel.id", source = "salesChannelId")
    @Mapping(target = "salesChannelId", source = "salesChannelId")
    @Mapping(target = "store.id", source = "storeId")
    @Mapping(target = "storeId", source = "storeId")
    @Mapping(target = "owner.id", source = "ownerId")
    @Mapping(target = "ownerId", source = "ownerId")
    @Mapping(target = "logisticsMode.id", source = "logisticsModeId")
    @Mapping(target = "logisticsModeId", source = "logisticsModeId")
    @Mapping(target = "instanceId.id", source = "instanceId")
    @Mapping(target = "shippingWarehouse.id", source = "shippingWarehouseId")
    @Mapping(target = "shippingWarehouseId", source = "shippingWarehouseId")
    @Mapping(target = "shippingWarehouse.code", source = "shippingWarehouseCode")
    @Mapping(target = "shippingWarehouseCode", source = "shippingWarehouseCode")
    @Mapping(target = "destCountryCode", source = "destCountryCode")
    @Mapping(target = "destCountry.code", source = "destCountryCode")
    @Mapping(target = "destWarehouseId", source = "destWarehouseId")
    @Mapping(target = "destWarehouse.id", source = "destWarehouseId")
    @Mapping(target = "destWarehouse.code", source = "destWarehouseCode")
    @Mapping(target = "destWarehouseCode", source = "destWarehouseCode")
    @Mapping(target = "salesStaffId", source = "salesStaffId")
    @Mapping(target = "salesStaff.operatorId", source = "salesStaffId")
    @Mapping(target = "planerStaffId", source = "salesStaffId")
    @Mapping(target = "planerStaff.operatorId", source = "salesStaffId")
    @Mapping(target = "shippingStaffId", source = "salesStaffId")
    @Mapping(target = "shippingStaff.operatorId", source = "salesStaffId")
    @Mapping(target = "created.operatorId", source = "createBy")
    @Mapping(target = "created.operateTime", source = "createTime")
    @Mapping(target = "updated.operatorId", source = "updateBy")
    @Mapping(target = "updated.operateTime", source = "updateTime")
    TransferOrder toDomain(TransferOrderPO po);

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<TransferOrder> toDomains(Collection<TransferOrderPO> pos);

    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "salesChannelId", source = "salesChannelId")
    @Mapping(target = "storeId", source = "storeId")
    @Mapping(target = "ownerId", source = "ownerId")
    @Mapping(target = "shippingWarehouseId", source = "shippingWarehouseId")
    @Mapping(target = "shippingWarehouseCode", source = "shippingWarehouseCode")
    @Mapping(target = "destWarehouseId", source = "destWarehouseId")
    @Mapping(target = "destCountryCode", source = "destCountryCode")
    @Mapping(target = "destWarehouseCode", source = "destWarehouseCode")
    @Mapping(target = "createBy", source = "created.operatorId")
    @Mapping(target = "createTime", source = "created.operateTime")
    @Mapping(target = "updateBy", source = "updated.operatorId")
    @Mapping(target = "updateTime", source = "updated.operateTime")
    @InheritInverseConfiguration(name = "toDomain")
    TransferOrderPO toPO(TransferOrder domain);

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<TransferOrderPO> toPOs(Collection<TransferOrder> domains);

    @Mapping(target = "id", source = "id")
    TransferOrderId toId(Integer id);

    @Mapping(target = "pageIndex", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "totalCount", source = "total")
    Paging<TransferOrder> toPagingDomains(Page<TransferOrderPO> pageList);

    default Integer toId(TransferOrderId id) {
        return Optional.ofNullable(id).map(TransferOrderId::id).orElse(null);
    }

    TransferOrder tsToCopy(TransferOrder ts);

    List<TransferOrderItem> itemListCopy(List<TransferOrderItem> itemsOld);

    TransferOrderItem itemCopy(TransferOrderItem itemOld);

    @Mapping(target = "discrepancyType", constant = "SIGNED")
    @Mapping(target = "warehouseId", source = "destWarehouse.id.id")
    @Mapping(target = "businessNo", source = "tsNo")
    @Mapping(target = "inboundNo", source = "shipmentId")
    @Mapping(target = "detailList", source = "items")
    @Mapping(target = "businessType", constant = "SECOND_LE")
    InventoryDiscrepancyAddCommand toInventoryDiscrepancyAddCommand(TransferOrder ts);

    @Mapping(target = "psku", source = "psku")
    @Mapping(target = "fnsku", source = "fnSku")
    @Mapping(target = "ownerId", source = "owner.id.id")
    @Mapping(target = "storeId", source = "store.id.id")
    @Mapping(target = "inventoryAttribute", constant = "0")
    @Mapping(target = "expectedQty", source = "qty")
    @Mapping(target = "actualQty", source = "putawayQty")
    @Mapping(target = "discrepancyReasonType", constant = "RECEIVING_DISCREPANCY")
    InventoryDiscrepancyDetailDto toInventoryDiscrepancyDetailDto(TransferOrderItem tsItem);

    List<InventoryDiscrepancyDetailDto> toInventoryDiscrepancyDetailDtos(Collection<TransferOrderItem> tsItems);

}
