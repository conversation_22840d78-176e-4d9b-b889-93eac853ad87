package com.renpho.erp.tms.infrastructure.persistence.transferorder.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.data.permission.aop.annotation.DataPermission;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.inbound.WarehouseType;
import com.renpho.erp.tms.domain.operator.Operator;
import com.renpho.erp.tms.domain.owner.Owner;
import com.renpho.erp.tms.domain.saleschannel.SalesChannel;
import com.renpho.erp.tms.domain.site.CountryRegion;
import com.renpho.erp.tms.domain.store.Store;
import com.renpho.erp.tms.domain.store.StoreId;
import com.renpho.erp.tms.domain.transferorder.*;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.erp.tms.domain.warehouse.Warehouse;
import com.renpho.erp.tms.domain.warehouse.WarehouseId;
import com.renpho.erp.tms.infrastructure.common.util.WarehouseProviderTypeUtil;
import com.renpho.erp.tms.infrastructure.persistence.dataperm.RoleLabelExpression;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.mapper.TransferOrderMapper;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderItemPO;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.TransferOrderPO;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderConverter;
import com.renpho.erp.tms.infrastructure.remote.countryregion.repository.CountryRegionLookup;
import com.renpho.erp.tms.infrastructure.remote.owner.repository.OwnerLookup;
import com.renpho.erp.tms.infrastructure.remote.saleschannel.repository.SalesChannelLookup;
import com.renpho.erp.tms.infrastructure.remote.store.repository.StoreLookup;
import com.renpho.erp.tms.infrastructure.remote.user.repository.OperatorLookup;
import com.renpho.erp.tms.infrastructure.remote.warehouse.repository.WarehouseLookup;
import com.renpho.karma.dto.Paging;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 调拨单查询实现
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Repository
@RequiredArgsConstructor
public class TransferOrderLookupImpl extends ServiceImpl<TransferOrderMapper, TransferOrderPO> implements TransferOrderLookup {

    private final TransferOrderConverter transferOrderConverter;
    private final CountryRegionLookup countryRegionLookup;
    private final SalesChannelLookup salesChannelLookup;
    private final OwnerLookup ownerLookup;
    private final WarehouseLookup warehouseLookup;
    private final StoreLookup storeLookup;
    private final WarehouseProviderTypeUtil warehouseProviderTypeUtil;
    private final TransferOrderItemRepository transferOrderItemRepository;
    private final OperatorLookup operatorLookup;

    @Override
    public Optional<TransferOrder> findById(TransferOrderId tsId) {
        return Optional.ofNullable(tsId).map(TransferOrderId::id)
                .flatMap(this::getOptById)
                .map(transferOrderConverter::toDomain)
                .map(List::of)
                .map(transferOrderItemRepository::findByTransferOrders)
                .stream()
                .flatMap(Collection::stream)
                .findAny();
    }

    @Override
    @DataPermission(condition = RoleLabelExpression.class)
    public Paging<TransferOrder> findPage(TransferOrderQuery condition) {

        List<Integer> tsIds = new ArrayList<>();
        //查询详情返回主表id
        if (Objects.nonNull(condition)
            && (StringUtils.isNotBlank(condition.getPsku())
                || StringUtils.isNotBlank(condition.getFnsku())
                || Objects.nonNull(condition.getIsBorrowed())
                || Objects.nonNull(condition.getIsRelabel())
                || StringUtils.isNotBlank(condition.getOutboundNo()))
        ) {

            tsIds.addAll(transferOrderItemRepository.findByPageQuery(condition));
            if (CollectionUtils.isEmpty(tsIds)) {
                return Paging.of(condition.getPageSize(), condition.getPageIndex());
            }
        }

        condition.setIds(tsIds);
        LambdaQueryChainWrapper<TransferOrderPO> wrapper = this.buildCondition(condition);
        Page<TransferOrderPO> pageList = wrapper.page(new Page<>(condition.getPageIndex(), condition.getPageSize()));
        Paging<TransferOrder> paging = transferOrderConverter.toPagingDomains(pageList);

        if (!CollectionUtils.isEmpty(paging.getRecords())) {
            // 填充详情
            List<TransferOrderId> ids = paging.getRecords().stream().map(TransferOrder::getId).toList();
            Map<TransferOrderId, List<TransferOrderItem>> itemMap = transferOrderItemRepository.findByTsIds(ids);
            for (TransferOrder order : paging.getRecords()) {
                order.setItems(itemMap.get(order.getTsId()));
            }
        }

        return paging;
    }

    @Override
    public List<TransferOrder> findList(TransferOrderQuery condition) {
        return List.of();
    }

    @Override
    public Optional<TransferOrder> findByTsNo(String tsNo) {
        if (StringUtils.isBlank(tsNo)) {
            return Optional.empty();
        }
        return lambdaQuery().eq(TransferOrderPO::getTsNo, tsNo).last("LIMIT 1")
                .oneOpt()
                .map(transferOrderConverter::toDomain)
                .map(List::of)
                .map(transferOrderItemRepository::findByTransferOrders)
                .stream()
                .flatMap(Collection::stream)
                .findAny();

    }

    @Override
    public List<TransferOrder> findByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            return List.of();
        }
        List<TransferOrderPO> pos = lambdaQuery().eq(TransferOrderPO::getOrderNo, orderNo).list();
        return transferOrderConverter.toDomains(pos);
    }

    @Override
    public List<TransferOrder> findByStatus(TransferOrderStatus status) {
        if (status == null) {
            return List.of();
        }
        List<TransferOrderPO> pos = lambdaQuery().eq(TransferOrderPO::getStatus, status.getValue()).list();
        return transferOrderConverter.toDomains(pos);
    }

    @Override
    public List<TransferOrderData> selectTsListByStatus(Integer status, WarehouseProviderType warehouseType) {
        List<TransferOrderData> list = this.baseMapper.selectTsListByStatus(status, warehouseType.name());
        if (CollectionUtils.isNotEmpty(list)) {
            return list;
        }

        return List.of();
    }

    @Override
    public List<TransferOrderData> selectTsListByTsNoList(List<String> needTsNoList) {
        if (CollectionUtils.isEmpty(needTsNoList)) {
            return List.of();
        }

        List<TransferOrderData> list = this.baseMapper.selectTsListByTsNoList(needTsNoList);
        if (CollectionUtils.isNotEmpty(list)) {
            return list;
        }
        return List.of();
    }

    @Override
    public Map<TransferOrderId, TransferOrder> findByIds(Collection<TransferOrderId> tsIds) {
        if (CollectionUtils.isEmpty(tsIds)) {
            return Map.of();
        }
        List<TransferOrderPO> pos = lambdaQuery().in(TransferOrderPO::getId, tsIds).list();
        List<TransferOrder> domains = transferOrderConverter.toDomains(pos);
        transferOrderItemRepository.findByTransferOrders(domains);
        return domains.stream().collect(Collectors.toMap(TransferOrder::getId, Function.identity()));
    }

    @Override
    public List<TransferOrder> findByWarehouseProviderType(WarehouseProviderType warehouseProviderType,
                                                           Collection<TransferOrderId> tsIds,
                                                           Collection<String> tsNos,
                                                           Collection<TransferOrderStatus> transferOrderStatuses,
                                                           SyncApiStatus apiStatus) {

        if (warehouseProviderType == null) {
            return List.of();
        }
        List<Warehouse> warehouses = warehouseLookup.findByTypeAndServiceProvider(
                warehouseProviderType.getWarehouseType(),
                warehouseProviderType.getServiceProvider()
        );

        if (CollectionUtils.isEmpty(warehouses)) {
            return List.of();
        }
        Set<Integer> warehouseIds = warehouses.stream().map(Warehouse::getId).map(WarehouseId::id).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(warehouseIds)) {
            return List.of();
        }

        if (warehouseProviderType.getWarehouseType() == WarehouseType.PLATFORM) {
            Set<Integer> storeWarehouseIds = warehouseProviderTypeUtil.findStoresByWarehouseProviderType(warehouseProviderType);
            warehouseIds.removeIf(Predicate.not(storeWarehouseIds::contains));
        }

        Set<Integer> ids = CollectionUtils.emptyIfNull(tsIds).stream()
                .filter(Objects::nonNull).map(TransferOrderId::id)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        Set<String> nos = CollectionUtils.emptyIfNull(tsNos).stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        Set<Integer> status = CollectionUtils.emptyIfNull(transferOrderStatuses).stream()
                .map(TransferOrderStatus::getValue)
                .collect(Collectors.toSet());

        List<TransferOrderPO> pos = lambdaQuery()
                .in(TransferOrderPO::getDestWarehouseId, warehouseIds)
                .in(CollectionUtils.isNotEmpty(ids), TransferOrderPO::getId, ids)
                .in(CollectionUtils.isNotEmpty(nos), TransferOrderPO::getTsNo, nos)
                .in(CollectionUtils.isNotEmpty(status), TransferOrderPO::getTsNo, status)
                .eq(apiStatus != null, TransferOrderPO::getSyncApiStatus, apiStatus)
                .list();
        return transferOrderConverter.toDomains(pos);
    }


    @Override
    public List<TransferOrder> findOperatorAssociations(Collection<TransferOrder> domains) {
        List<Operator> operators = CollectionUtils.emptyIfNull(domains).stream()
                .filter(Objects::nonNull)
                .flatMap(p -> Stream.of(Optional.ofNullable(p.getCreated()),
                        Optional.ofNullable(p.getUpdated()),
                        Optional.ofNullable(p.getSalesStaff()),
                        Optional.ofNullable(p.getPlanerStaff()),
                        Optional.ofNullable(p.getShippingStaff())))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();
        if (CollectionUtils.isEmpty(operators)) {
            return List.of();
        }

        operatorLookup.findAndSetByIds(operators);
        return new ArrayList<>(domains);
    }

    @Override
    public List<TransferOrder> findStoreAssociations(Collection<TransferOrder> domains) {
        Map<StoreId, List<TransferOrder>> storeIds = CollectionUtils.emptyIfNull(domains).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(ts -> ts.getStore().getId()));
        if (MapUtils.isEmpty(storeIds)) {
            return new ArrayList<>(CollectionUtils.emptyIfNull(domains));
        }
        Map<StoreId, Store> stores = storeLookup.findByIds(storeIds.keySet());
        for (List<TransferOrder> list : storeIds.values()) {
            for (TransferOrder ts : list) {
                Store store = stores.get(ts.getStore().getId());
                ts.setStore(store);
            }
        }

        //详情店铺
        Map<StoreId, List<TransferOrderItem>> detailStoreIds =
                CollectionUtils.emptyIfNull(domains).stream()
                        .filter(Objects::nonNull)
                        .flatMap(ts -> CollectionUtils.emptyIfNull(ts.getItems()).stream()) // 平铺 items
                        .filter(Objects::nonNull)
                        .collect(Collectors.groupingBy(item -> item.getBorrowedStore().getId()));
        if (!MapUtils.isEmpty(detailStoreIds)) {
            Map<StoreId, Store> detailStores = storeLookup.findByIds(detailStoreIds.keySet());
            for (List<TransferOrderItem> list : detailStoreIds.values()) {
                for (TransferOrderItem ts : list) {
                    Store store = detailStores.get(ts.getStore().getId());
                    ts.setStore(store);
                }
            }
        }

        return new ArrayList<>(domains);
    }

    @Override
    public List<TransferOrder> findWarehouseAssociations(Collection<TransferOrder> domains) {
        List<Warehouse> warehouses = CollectionUtils.emptyIfNull(domains).stream()
                .filter(Objects::nonNull)
                .flatMap(p -> Stream.of(Optional.ofNullable(p.getDestWarehouse()),
                        Optional.ofNullable(p.getShippingWarehouse())))
                .filter(Optional::isPresent)
                .map(Optional::get).toList();
        if (CollectionUtils.isEmpty(warehouses)) {
            return List.of();
        }
        warehouseLookup.findAndSetByIds(warehouses);

        return new ArrayList<>(domains);
    }

    @Override
    public List<TransferOrder> findOwnerAssociations(Collection<TransferOrder> domains) {
        List<Owner> allOwners = Stream.concat(
                CollectionUtils.emptyIfNull(domains).stream()
                        .map(TransferOrder::getOwner)
                        .filter(Objects::nonNull),
                CollectionUtils.emptyIfNull(domains).stream()
                        .flatMap(tr -> CollectionUtils.emptyIfNull(tr.getItems()).stream())
                        .map(TransferOrderItem::getBorrowedOwner)
                        .filter(Objects::nonNull)
        ).toList();

        if (CollectionUtils.isNotEmpty(allOwners)) {
            ownerLookup.findAndSetByIds(allOwners);
        }

        return new ArrayList<>(domains);
    }

    @Override
    public List<TransferOrder> findSalesChannelAssociations(Collection<TransferOrder> domains) {
        List<SalesChannel> salesChannels = CollectionUtils.emptyIfNull(domains).stream()
                .filter(Objects::nonNull)
                .flatMap(p -> Stream.of(Optional.ofNullable(p.getSalesChannel())))
                .filter(Optional::isPresent)
                .map(Optional::get).toList();
        if (CollectionUtils.isEmpty(salesChannels)) {
            return List.of();
        }
        salesChannelLookup.findAndSetByIds(salesChannels);

        return new ArrayList<>(domains);
    }

    @Override
    public List<TransferOrder> findCountryRegionAssociations(Collection<TransferOrder> domains) {
        List<CountryRegion> countryRegions = CollectionUtils.emptyIfNull(domains).stream()
                .filter(Objects::nonNull)
                .flatMap(p -> Stream.of(Optional.ofNullable(p.getDestCountry())))
                .filter(Optional::isPresent)
                .map(Optional::get).toList();
        if (CollectionUtils.isEmpty(countryRegions)) {
            return List.of();
        }
        countryRegionLookup.findAndSetByCodes(countryRegions);

        return new ArrayList<>(domains);
    }

    @Override
    @DataPermission(condition = RoleLabelExpression.class)
    public Map<Integer, Object> countByStatus() {
        List<Integer> tsIds = Optional.ofNullable(transferOrderItemRepository.list())
                .map(list -> list.stream()
                        .map(TransferOrderItemPO::getTsId)
                        .toList())
                .orElse(Collections.emptyList());
        if (CollectionUtils.isEmpty(tsIds))
            return new HashMap<>();

        QueryWrapper<TransferOrderPO> qw = new QueryWrapper<>();
        LambdaQueryWrapper<TransferOrderPO> lambdaQueryWrapper =
                qw.select(" status, COUNT(*) as count ").lambda()
                        .in(TransferOrderPO::getId, tsIds);
        lambdaQueryWrapper.groupBy(TransferOrderPO::getStatus);
        List<TransferOrderPO> result = this.baseMapper.selectList(lambdaQueryWrapper);
        Map<Integer, Object> map = new HashMap<>();
        result.forEach(po -> map.put(po.getStatus(), po.getCount()));
        return map;
    }


    @Override
    public Optional<TransferOrder> detail(TransferOrderQuery condition) {
        Optional<TransferOrder> orderOptional = Optional.empty();
        if (Objects.nonNull(condition.getId())) {
            orderOptional = this.findById(new TransferOrderId(condition.getId()));
        } else if (StringUtils.isNotBlank(condition.getTsNo())) {
            orderOptional = this.findByTsNo(condition.getTsNo());
        }
        if (orderOptional.isEmpty()) {
            return orderOptional;
        }

        TransferOrder order = orderOptional.get();
        List<TransferOrderItem> items = transferOrderItemRepository.findByTsId(order.getId());
        order.setItems(items);
        return Optional.of(order);
    }

    @Override
    public List<TransferOrder> findByTsNos(Collection<String> tsNos) {
        Set<String> nos = CollectionUtils.emptyIfNull(tsNos).stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(nos)) {
            return List.of();
        }

        List<TransferOrderPO> pos = lambdaQuery().in(TransferOrderPO::getTsNo, nos).list();
        List<TransferOrder> domains = transferOrderConverter.toDomains(pos);
        transferOrderItemRepository.findByTransferOrders(domains);
        return domains;
    }

    private LambdaQueryChainWrapper<TransferOrderPO> buildCondition(TransferOrderQuery condition) {
        assert condition.getDestWarehouseId() != null;
        return lambdaQuery()
                .in(CollectionUtils.isNotEmpty(condition.getIds()), TransferOrderPO::getId, condition.getIds())
                .eq(StringUtils.isNotBlank(condition.getTsNo()), TransferOrderPO::getTsNo, condition.getTsNo())
                .eq(Objects.nonNull(condition.getShippingWarehouseId()),
                        TransferOrderPO::getShippingWarehouseId, condition.getShippingWarehouseId())
                .eq(condition.getDestWarehouseId() != null,
                        TransferOrderPO::getDestWarehouseId, condition.getDestWarehouseId())
                .eq(StringUtils.isNotBlank(condition.getTsBizType()), TransferOrderPO::getTsBizType, condition.getTsBizType())
                .in(CollectionUtils.isNotEmpty(condition.getType()), TransferOrderPO::getType, condition.getType())
                .eq(condition.getOwnerId() != null,
                        TransferOrderPO::getOwnerId, condition.getOwnerId())
                .in(CollectionUtils.isNotEmpty(condition.getStoreIds()),
                        TransferOrderPO::getStoreId, condition.getStoreIds())
                .eq(condition.getSalesChannelId() != null,
                        TransferOrderPO::getSalesChannelId, condition.getSalesChannelId())
                .in(CollectionUtils.isNotEmpty(condition.getDestCountryCode()),
                        TransferOrderPO::getDestCountryCode, condition.getDestCountryCode())
                .eq(StringUtils.isNotBlank(condition.getShipmentId()), TransferOrderPO::getShipmentId, condition.getShipmentId())
                .eq(StringUtils.isNotBlank(condition.getOrderNo()), TransferOrderPO::getOrderNo, condition.getOrderNo())
                .eq(StringUtils.isNotBlank(condition.getRefNo()), TransferOrderPO::getRefNo, condition.getRefNo())
                .eq(condition.getIsAllowedShipping() != null, TransferOrderPO::getIsAllowedShipping, condition.getIsAllowedShipping())
                .ge(condition.getEstimatedDepartureStartTime() != null,
                        TransferOrderPO::getEstimatedDepartureTime, condition.getEstimatedDepartureStartTime())
                .le(condition.getEstimatedDepartureEndTime() != null,
                        TransferOrderPO::getEstimatedDepartureTime, condition.getEstimatedDepartureEndTime())
                .ge(condition.getEstimatedDeliveryStartTime() != null,
                        TransferOrderPO::getEstimatedDeliveryTime, condition.getEstimatedDeliveryStartTime())
                .le(condition.getEstimatedDeliveryEndTime() != null,
                        TransferOrderPO::getEstimatedDeliveryTime, condition.getEstimatedDeliveryEndTime());
    }

    @Override
    public List<TransferOrderData> selectTsListWithItemOutboundNo(TransferOrderStatus status, WarehouseProviderType warehouseType) {
        return this.baseMapper.selectTsListWithItemOutboundNo(status.getValue(), warehouseType.getServiceProvider());
    }

    @Override
    public List<TransferOrder> findByProcessInstanceId(String processInstanceId) {
        if (StringUtils.isBlank(processInstanceId)) {
            return List.of();
        }

        List<TransferOrderPO> orders = lambdaQuery().eq(TransferOrderPO::getInstanceId, processInstanceId).list();
        if (CollectionUtils.isEmpty(orders)) return List.of();

        return transferOrderConverter.toDomains(orders);
    }

}
