package com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.renpho.erp.data.trans.MultiLanguage;
import com.renpho.erp.ims.client.feign.inventory.command.InventoryDiscrepancyAddCommand;
import com.renpho.erp.ims.client.feign.inventory.dto.InventoryDiscrepancyDetailDto;
import com.renpho.erp.tms.domain.transportrequest.QcResult;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestId;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestStatus;
import com.renpho.erp.tms.domain.transportrequest.dto.TransportRequestSignInResponse;
import com.renpho.erp.tms.infrastructure.common.converter.BooleanConverter;
import com.renpho.erp.tms.infrastructure.common.converter.MultiLanguageConverter;
import com.renpho.erp.tms.infrastructure.common.converter.TransportOrderStatusConverter;
import com.renpho.erp.tms.infrastructure.persistence.firstleg.mode.po.converter.FirstLegModeConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.TransportRequestOrderPo;
import com.renpho.erp.tms.infrastructure.remote.currency.converter.CurrencyConverter;
import com.renpho.erp.tms.infrastructure.remote.owner.converter.OwnerConverter;
import com.renpho.erp.tms.infrastructure.remote.product.converter.ProductConverter;
import com.renpho.erp.tms.infrastructure.remote.product.converter.ProductPriceConverter;
import com.renpho.erp.tms.infrastructure.remote.purchasesupplier.PurchaseSupplierConverter;
import com.renpho.erp.tms.infrastructure.remote.saleschannel.converter.SalesChannelConverter;
import com.renpho.erp.tms.infrastructure.remote.store.converter.StoreConverter;
import com.renpho.karma.dto.Paging;
import org.mapstruct.*;

import java.util.Collection;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.stream.Stream;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {PurchaseSupplierConverter.class, SalesChannelConverter.class,
                StoreConverter.class, OwnerConverter.class,
                PurchaseSupplierConverter.class,
                ProductConverter.class,
                BooleanConverter.class,
                ProductPriceConverter.class,
                TransportOrderStatusConverter.class,
                TransportRequestOrderItemConverter.class,
                CurrencyConverter.class,
                MultiLanguageConverter.class,
                TransportRequestOrderItemConverter.class,
                FirstLegModeConverter.class})
public interface TransportRequestConverter {

    @Mapping(target = "id.id", source = "id")
    @Mapping(target = "item.trId", source = "id")
    @Mapping(target = "firstLegMode.id.id", source = "firstLegModeId")
    @Mapping(target = "salesChannel.id.id", source = "salesChannelId")
    @Mapping(target = "product.psku", source = "psku")
    @Mapping(target = "product.id", source = "productId")
    @Mapping(target = "product.fnSku", source = "fnSku")
    @Mapping(target = "store.id.id", source = "storeId")
    @Mapping(target = "transportOrderId.id", source = "toId")
    @Mapping(target = "owner.id.id", source = "ownerId")
    @Mapping(target = "destCountry.code", source = "destCountryCode")
    @Mapping(target = "destWarehouse.id.id", source = "destWarehouseId")
    @Mapping(target = "destWarehouseCode", source = "destWarehouseCode")
    @Mapping(target = "shippingWarehouse.id.id", source = "shippingWarehouse")
    @Mapping(target = "purchaseSupplier.id.id", source = "purchaseSupplierId")
    @Mapping(target = "purchaseSupplier.supplierCode", source = "purchaseSupplierCode")
    @Mapping(target = "purchaseSupplier.shortName", source = "purchaseSupplierShortName")
    @Mapping(target = "purchaseSupplier.name", source = "purchaseSupplierNameCn")
    @Mapping(target = "purchaseSupplier.enName", source = "purchaseSupplierNameEn")
    @Mapping(target = "purchaseSupplier.names", source = "po")
    @Mapping(target = "purchasingCompany.id", source = "purchasingCompanyId")
    @Mapping(target = "salesStaff.operatorId.id", source = "salesStaffId")
    @Mapping(target = "planningStaff.operatorId.id", source = "planningStaffId")
    @Mapping(target = "purchaseStaff.operatorId.id", source = "purchaseStaffId")
    @Mapping(target = "created.operatorId.id", source = "createBy")
    @Mapping(target = "created.operateTime", source = "createTime")
    @Mapping(target = "updated.operatorId.id", source = "updateBy")
    @Mapping(target = "updated.operateTime", source = "updateTime")
    @Mapping(target = "estimatedFreightCurrency.id.code", source = "freightCurrency")
    @Mapping(target = "estimatedTaxCurrency.id.code", source = "taxCurrency")
    @Mapping(target = "quantity", source = "totalQty")
    @Mapping(target = "boxQty", source = "totalBoxQty")
    @Mapping(target = "totalBoxVolume", source = "totalVolume")
    @Mapping(target = "shippingCompany.shortName", source = "shippingCompany")
    TransportRequest toDomain(TransportRequestOrderPo po);

    @Mapping(target = "pageIndex", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "totalCount", source = "total")
    Paging<TransportRequest> toPageDomains(Page<TransportRequestOrderPo> page);

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<TransportRequest> toDomains(Collection<TransportRequestOrderPo> pos);

    @InheritInverseConfiguration(name = "toDomain")
    @Mapping(target = "fnSku", source = "product.fnSku")
    TransportRequestOrderPo toPo(TransportRequest transportRequest);

    @Mapping(target = "id", source = "id")
    TransportRequestId toId(Integer id);

    default Integer toId(TransportRequestId id) {
        return Optional.ofNullable(id).map(TransportRequestId::id).orElse(null);
    }

    default TransportRequestStatus toStatus(Integer status) {
        return TransportRequestStatus.fromValue(status);
    }

    default Integer toStatus(TransportRequestStatus status) {
        return Optional.ofNullable(status).map(TransportRequestStatus::getValue).orElse(null);
    }

    default QcResult toQcResult(Integer result) {
        return QcResult.fromValue(result);
    }

    default Integer toQcResult(QcResult result) {
        return Optional.ofNullable(result).map(QcResult::getValue).orElse(null);
    }


    default List<MultiLanguage> toMultiLanguages(TransportRequestOrderPo po) {
        Optional<MultiLanguage> cnName = Optional.ofNullable(po.getPurchaseSupplierNameCn())
                .map(name -> MultiLanguageConverter.toMultiLanguage(name, Locale.SIMPLIFIED_CHINESE));
        Optional<MultiLanguage> enName = Optional.ofNullable(po.getPurchaseSupplierNameEn())
                .map(name -> MultiLanguageConverter.toMultiLanguage(name, Locale.US));

        return Stream.of(cnName, enName).filter(Optional::isPresent).map(Optional::get).toList();
    }

    TransportRequest trToCopy(TransportRequest tr);

    List<TransportRequestSignInResponse> toSignInResponses(List<TransportRequestOrderPo> pos);

    @Mapping(target = "discrepancyType", constant = "SIGNED")
    @Mapping(target = "warehouseId", source = "destWarehouse.id.id")
    @Mapping(target = "businessNo", source = "trNo")
    @Mapping(target = "inboundNo", source = "shipmentId")
    @Mapping(target = "businessType", constant = "HEAD_LEG")
    @Mapping(target = "relatedNo", source = "toNo")
    InventoryDiscrepancyAddCommand toInventoryDiscrepancyAddCommand(TransportRequest tr);

    @Mapping(target = "psku", source = "psku")
    @Mapping(target = "fnsku", source = "fnSku")
    @Mapping(target = "ownerId", source = "owner.id.id")
    @Mapping(target = "storeId", source = "store.id.id")
    @Mapping(target = "inventoryAttribute", constant = "0")
    @Mapping(target = "expectedQty", source = "quantity")
    @Mapping(target = "actualQty", source = "shelvedQuantity")
    @Mapping(target = "discrepancyReasonType", constant = "RECEIVING_DISCREPANCY")
    InventoryDiscrepancyDetailDto toInventoryDiscrepancyDetailDto(TransportRequest tr);

    default InventoryDiscrepancyAddCommand buildInventoryDiscrepancyAddCommand(TransportRequest tr) {
        InventoryDiscrepancyAddCommand command = toInventoryDiscrepancyAddCommand(tr);
        InventoryDiscrepancyDetailDto detailDto = toInventoryDiscrepancyDetailDto(tr);
        command.setDetailList(List.of(detailDto));
        return command;
    }
}
