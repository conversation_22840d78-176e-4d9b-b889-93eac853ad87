package com.renpho.erp.tms.infrastructure.common.oplog;


public class LogModule {

    public static final String VOLUMETRIC_FACTOR_LOG_MODULE = "VOLUMETRIC_FACTOR";
    public static final String PORT_INFO_LOG_MODULE = "PORT_INFO";
    public static final String FIRST_LEG_MODE_LOG_MODULE = "FIRST_LEG_MODE";
    public static final String FIRST_LEG_MODE_TRANSIT_TIME_LOG_MODULE = "FIRST_LEG_MODE_TRANSIT_TIME";
    public static final String CUSTOMS_INFO_LOG_MODULE = "CUSTOMS_INFO";
    public static final String TRANSPORT_REQUEST = "TRANSPORT_REQUEST";
    public static final String TRANSPORT_ORDER = "TRANSPORT_ORDER";
    public static final String SHIPPING_FEE_FORMULA_CONFIG = "SHIPPING_FEE_FORMULA_CONFIG";
    public static final String SSCC_REGISTER_MATERIAL_LOG_MODULE = "SSCC";
    public static final String GEN_VC_SSCC_RECORD_LOG_MODULE = "GEN_VC_SSCC_RECORD";
    public static final String GEN_WMT_SSCC_RECORD_LOG_MODULE = "GEN_WMT_SSCC_RECORD";
    public static final String TRANSFER_ODER = "TRANSFER_ODER";

    public interface CommonDesc {

        String INSERT_OPERATOR = "Insert";
        String INSERT_DESC = "新增";

        String SUBMIT_OPERATOR = "Submit";
        String SUBMIT_DESC = "提交";

        String EDIT_OPERATOR = "Edit";
        String EDIT_DESC = "编辑";

        String VOID_OPERATOR = "Void";
        String VOID_DESC = "作废";

        String WITHDRAW_OPERATOR = "Withdraw";
        String WITHDRAW_DESC = "撤回";

        String REVIEW_OPERATOR = "Review";
        String REVIEW_DESC = "审核";

        String UPLOAD_OPERATOR = "Upload";
        String UPLOAD_DESC = "导入";

        String DOWNLOAD_OPERATOR = "Download";
        String DOWNLOAD_DESC = "导出";

        String CANCEL_OPERATOR = "Cancel";
        String CANCEL_DESC = "取消";

        String CONFIRM_OPERATOR = "Confirm";
        String CONFIRM_DESC = "确认";

        String UPLOAD_FILE_OPERATOR = "Upload_File";
        String UPLOAD_FILE_DESC = "上传文件";


        String DELETE_OPERATOR = "Delete";
        String DELETE_DESC = "删除";

        String ENABLE_OPERATOR = "Enable";
        String ENABLE_DESC = "启用";

        String DISABLE_OPERATOR = "Disable";
        String DISABLE_DESC = "禁用";

        String DOWNLOAD_ATTACHMENT_OPERATOR = "Download_Attachment";
        String DOWNLOAD_ATTACHMENT_DESC = "下载附件";

        String GEN_SSCC_OPERATOR = "Generate_SSCC";
        String GEN_SSCC_DESC = "生成SSCC";

        String DOWNLOAD_SSCC_OPERATOR = "Download_SSCC";
        String DOWNLOAD_SSCC_DESC = "下载SSCC";

        String REVIEW_PASS_OPERATOR = "Review_Pass";
        String REVIEW_PASS_DESC = "审核通过";

        String REVIEW_REJECTED_OPERATOR = "Review_Rejected";
        String REVIEW_REJECTED_DESC = "审核拒绝";
    }

    public interface TransportRequest {
        String CONSOLIDATION_OPERATOR = "consolidation";
        String CONSOLIDATION_DESC = "拼柜";

        String CANCEL_CONSOLIDATION_OPERATOR = "cancel_the_consolidation";
        String CANCEL_CONSOLIDATION_DESC = "取消拼柜";

        String UPLOAD_INBOUND_ORDER_NO_OPERATOR = "upload_inbound_order_no";
        String UPLOAD_INBOUND_ORDER_NO_DESC = "上传入库单";
        String BOOKING_DESC = "订舱";
        String CANCEL_TR_DESC = "作废TR单";
    }

    public interface TransportOrder {
        String BOOKING_DESC = "订舱";
        String HANDOVER_DESC = "交货";
        String MARK_DEPARTURE_DESC = "标记离港";
        String MARK_ARRIVAL_DESC = "标记到港";
        String MARK_DELIVERY_DESC = "标记派送";
        String EXPORT_PACKING_LIST_DESC = "导出箱单";
        String EXPORT_INVOICE_DESC = "导出发票";
    }

    public interface TransferOrder {
        String CANCEL_TS_DESC = "订舱";

        String UPLOAD_INBOUND_ORDER_NO_OPERATOR = "upload_inbound_order_no";
        String UPLOAD_INBOUND_ORDER_NO_DESC = "上传入库单";

        String MARK_RELABEL_FINISH_OPERATOR = "mark_relabel_finish";
        String MARK_RELABEL_FINISH_DESC = "标记换标完成";

        String MARK_SHIPMENT_OPERATOR = "mark_shipment";
        String MARK_SHIPMENT_DESC = "标记发货";
    }
}
