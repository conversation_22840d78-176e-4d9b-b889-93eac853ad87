package com.renpho.erp.pds.application.mq;

import com.renpho.erp.bpm.api.dto.ProcessResultDto;
import com.renpho.erp.pds.application.product.manager.service.PdsProductManagerSrv;
import com.renpho.erp.pds.application.utils.TraceIdUtil;
import com.renpho.erp.pds.domain.common.PdsManagerReviewStatus;
import com.renpho.karma.json.JSONKit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.function.Consumer;

@Slf4j
@Component
public class BpmCallbackConsumer {
    @Autowired
    private PdsProductManagerSrv pdsProductManagerSrv;

    @Bean
    public Consumer<Message<String>> auditProduct() {
        return msg -> {
            try {
                String traceId = TraceIdUtil.generateTraceId();
                TraceIdUtil.setTraceId(traceId);

                log.info("收到BPM回传的初始消息 - msg header: {}, json: {}", msg.getHeaders(), msg.getPayload());
                String message = msg.getPayload();
                log.info("接收到BPM系统同步至OMS的信息{} ", message);
                ProcessResultDto processResultDto = JSONKit.parseObject(message, ProcessResultDto.class);
                // pskuId
                String bizId = processResultDto.getBizId();
                // 审核通过
                if (Objects.equals(processResultDto.getResult(), 1)) {
                    pdsProductManagerSrv.simulatedApproval(Integer.parseInt(bizId), PdsManagerReviewStatus.APPROVED.getCode());
                    return;
                }
                // 拒绝
                if (Objects.equals(processResultDto.getResult(), 2)) {
                    pdsProductManagerSrv.simulatedApproval(Integer.parseInt(bizId), PdsManagerReviewStatus.REJECTED.getCode());
                    return;
                }
                // 撤回
                if (Objects.equals(processResultDto.getResult(), 4)) {
                    pdsProductManagerSrv.simulatedApproval(Integer.parseInt(bizId), PdsManagerReviewStatus.DRAFT.getCode());
                    return;
                }
            } catch (Exception e) {
                log.error("处理采购供应商同步消息异常", e);
            } finally {
                TraceIdUtil.clear();
            }
        };
    }
}
