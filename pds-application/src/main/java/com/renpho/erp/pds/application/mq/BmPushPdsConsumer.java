package com.renpho.erp.pds.application.mq;

import com.alibaba.fastjson2.JSON;
import com.renpho.erp.pds.application.product.manager.service.PdsProductManagerSrv;
import com.renpho.erp.pds.application.utils.TraceIdUtil;
import com.renpho.erp.pds.domain.product.srm.request.ProductBusinessManagerRequest;
import com.renpho.erp.stream.StreamCommonConstants;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;

import java.util.function.Consumer;

/**
 * 产品商务管理->PDS产品管理
 *
 * <AUTHOR>
 * @since 2024/12/10
 */
@Slf4j
@Configuration
public class BmPushPdsConsumer {

	@Resource
	private PdsProductManagerSrv pdsProductManagerSrv;

	@Bean
	public Consumer<Message<ProductBusinessManagerRequest>> bmSyncPdsConsumer() {
		return msg -> {
			try {
				String traceId = TraceIdUtil.generateTraceId();
				TraceIdUtil.setTraceId(traceId);

				ProductBusinessManagerRequest request = msg.getPayload();
				log.info("收到采购供应商同步消息, msg header: [{}],supplierRequest：【{}】 ", msg.getHeaders(), JSON.toJSONString(request));
				String tag = String.valueOf(msg.getHeaders().get(StreamCommonConstants.HEADER_TAGS));

				pdsProductManagerSrv.syncProductManager(request, tag);
			} catch (Exception e) {
				log.error("处理采购供应商同步消息异常", e);
			} finally {
				TraceIdUtil.clear();
			}
		};
	}

}
