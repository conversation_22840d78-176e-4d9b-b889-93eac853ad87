package com.renpho.erp.pds.application.brand.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.renpho.erp.mdm.client.store.RemoteStoreService;
import com.renpho.erp.mdm.client.store.command.StoreIdsQuery;
import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.pds.application.brand.dto.PdsBrandUserDetailResponse;
import com.renpho.erp.pds.application.brand.dto.PdsBrandUsersResponse;
import com.renpho.erp.pds.application.common.service.PdsCommonSrv;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsBrandQueryRequest;
import com.renpho.erp.pds.domain.brand.PdsBrand;
import com.renpho.erp.pds.domain.brand.PdsBrandRepository;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsBrandViewRequest;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsBrandWithStoreResponse;
import com.renpho.erp.pds.infrastructure.persistence.mapper.PdsBrandMapper;
import com.renpho.erp.pds.infrastructure.persistence.mapper.PdsBrandStoresMapper;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsBrandPo;
import com.renpho.erp.pds.infrastructure.utils.PagingUtils;
import com.renpho.erp.smc.client.RemoteUserDetailsService;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;

import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 品牌服务接口.
 *
 * <AUTHOR>
 * @since 2024.9.14
 */
@Service
public class PdsBrandSrv {

    @Autowired
    private PdsBrandMapper pdsBrandMapper;

    @Autowired
    private PdsBrandStoresMapper pdsBrandStoresMapper;

    @Autowired
    private PdsBrandRepository pdsBrandRepository;

    @Autowired
    private RemoteStoreService remoteStoreService;

    @Autowired
    private RemoteUserDetailsService remoteUserDetailsService;

    @Autowired
    private PdsCommonSrv pdsCommonSrv;


    /**
     * 搜索用户信息
     *
     * @param keyword  关键字
     * @param createBy 是否是创建人，否的话就是更新人
     * @return 用户信息
     */
    public PdsBrandUsersResponse searchUsers(String keyword, boolean createBy) {
        PdsBrandUsersResponse response = new PdsBrandUsersResponse();

        List<Integer> userIds = null;
        if (createBy) {
            userIds = pdsBrandMapper.selectDistinctCreateBy();
        } else {
            userIds = pdsBrandMapper.selectDistinctUpdateBy();
        }

        if (userIds != null && !userIds.isEmpty()) {
            // TODO 用户接口存在重复的数据，需要去重
            Map<Integer, Boolean> userMap = new HashMap<>();
            List<OumUserInfoRes> userListInfos = pdsCommonSrv.getUserListInfo(userIds);
            if (userListInfos != null) {
                for (OumUserInfoRes userInfo : userListInfos) {
                    if (StringUtils.isNotBlank(keyword)) {
                        if (!userInfo.getName().toUpperCase().contains(keyword.toUpperCase())) {
                            continue;
                        }
                    }

                    if (userMap.containsKey(userInfo.getId())) {
                        continue;
                    } else {
                        userMap.put(userInfo.getId(), true);
                    }

                    PdsBrandUserDetailResponse user = new PdsBrandUserDetailResponse();
                    user.setId(userInfo.getId());
                    user.setUsername(userInfo.getName());
                    user.setCode(userInfo.getCode());
                    response.addUser(user);
                }
            }
        }

        return response;
    }

    /**
     * 根据主键获取品牌信息
     *
     * @param id 主键
     * @return 品牌信息
     */
    public PdsBrandPo getPdsBrandDetail(Integer id) {
        if (id == null) {
            return null;
        }
        return pdsBrandMapper.selectById(id);
    }

    /**
     * 添加品牌数据
     *
     * @param pdsBrand 品牌数据
     * @return 主键id
     */
    public int add(PdsBrand pdsBrand) {
        return pdsBrandRepository.savePdsBrand(pdsBrand);
    }

    /**
     * 跟新品牌数据
     *
     * @param pdsBrand 品牌数据
     * @return 受影响条数
     */
    public int update(PdsBrand pdsBrand) {
        return pdsBrandRepository.updatePdsBrand(pdsBrand);
    }

    /**
     * 列表分页查询，多表
     *
     * @param pdsBrandQuery 查询参数
     * @return 结果
     */
    public Paging<PdsBrandWithStoreResponse> queryWithPage(PdsBrandQueryRequest pdsBrandQuery) {
        Page<PdsBrandPo> page = new Page<PdsBrandPo>(pdsBrandQuery.getPageIndex(), pdsBrandQuery.getPageSize());
        IPage<PdsBrandWithStoreResponse> iPage = pdsBrandMapper.selectBrandWithStores(page, pdsBrandQuery);

        return PagingUtils.convertToPaging(iPage);
    }

    /**
     * 列表分页查询,单表
     *
     * @param pdsBrandQuery 查询参数
     * @return 结果
     */
    public Paging<PdsBrandPo> queryWithPageWithSingle(PdsBrandQueryRequest pdsBrandQuery) {
        Page<PdsBrandPo> page = new Page<PdsBrandPo>(pdsBrandQuery.getPageIndex(), pdsBrandQuery.getPageSize());

        // 创建 QueryWrapper 对象
        LambdaQueryWrapper<PdsBrandPo> queryWrapper = new LambdaQueryWrapper<PdsBrandPo>();
        if (StringUtils.isNotBlank(pdsBrandQuery.getBrand())) {
            queryWrapper.eq(PdsBrandPo::getBrand, pdsBrandQuery.getBrand());
        }
        if (StringUtils.isNotBlank(pdsBrandQuery.getCode())) {
            queryWrapper.eq(PdsBrandPo::getCode, pdsBrandQuery.getCode());
        }
        if (StringUtils.isNotBlank(pdsBrandQuery.getRegisteredEntity())) {
            queryWrapper.eq(PdsBrandPo::getRegisteredEntity, pdsBrandQuery.getRegisteredEntity());
        }
        if (StringUtils.isNotBlank(pdsBrandQuery.getRemark())) {
            queryWrapper.eq(PdsBrandPo::getRemark, pdsBrandQuery.getRemark());
        }
        if (pdsBrandQuery.getStatus() != null) {
            queryWrapper.eq(PdsBrandPo::getStatus, pdsBrandQuery.getStatus());
        }
        if (pdsBrandQuery.getCreateBy() != null) {
            queryWrapper.eq(PdsBrandPo::getCreateBy, pdsBrandQuery.getCreateBy());
        }
        if (pdsBrandQuery.getUpdateBy() != null) {
            queryWrapper.eq(PdsBrandPo::getUpdateBy, pdsBrandQuery.getUpdateBy());
        }
        if (pdsBrandQuery.getCreateTime() != null) {
            queryWrapper.ge(PdsBrandPo::getCreateTime, pdsBrandQuery.getCreateTime());
        }
        if (pdsBrandQuery.getUpdateTime() != null) {
            queryWrapper.ge(PdsBrandPo::getUpdateTime, pdsBrandQuery.getUpdateTime());
        }


        IPage<PdsBrandPo> iPage = pdsBrandMapper.selectPage(page, queryWrapper);
        return PagingUtils.convertToPaging(iPage);
    }

    /**
     * 禁用、启用
     *
     * @param pdsBrand 状态数据
     * @return 受影响条数
     */
    public int changeStatus(PdsBrand pdsBrand) {
        return pdsBrandRepository.updatePdsBrand(pdsBrand);
    }

    /**
     * 根据店铺Id获取店铺信息
     *
     * @param storeIds 店铺Id集合
     * @return 店铺信息
     */
    public List<StoreVo> findStores(List<Integer> storeIds) {
        StoreIdsQuery storeIdsQuery = new StoreIdsQuery();
        storeIdsQuery.setIds(storeIds);
        R<List<StoreVo>> storesResult = remoteStoreService.findByIds(storeIdsQuery);
        if (storesResult.isSuccess()) {
            return storesResult.getData();
        }
        return null;
    }

    /**
     * 查询品牌主数据接口
     *
     * @param viewRequest 参数
     * @return 结果集
     */
    public List<PdsBrandPo> viewList(PdsBrandViewRequest viewRequest) {
        // 创建 QueryWrapper 对象
        LambdaQueryWrapper<PdsBrandPo> queryWrapper = new LambdaQueryWrapper<PdsBrandPo>();
        if (CollectionUtils.isNotEmpty(viewRequest.getBrandIds())) {
            queryWrapper.in(PdsBrandPo::getId, viewRequest.getBrandIds());
        }
        if (StringUtils.isNotBlank(viewRequest.getBrand())) {
            queryWrapper.eq(PdsBrandPo::getBrand, viewRequest.getBrand());
        }
        if (StringUtils.isNotBlank(viewRequest.getCode())) {
            queryWrapper.eq(PdsBrandPo::getCode, viewRequest.getCode());
        }
        if (viewRequest.getStatus() != null) {
            queryWrapper.eq(PdsBrandPo::getStatus, viewRequest.getStatus());
        }

        //TODO 暂时限制1000
        Page<PdsBrandPo> page = new Page<PdsBrandPo>(1, 1000);
        IPage<PdsBrandPo> iPage = pdsBrandMapper.selectPage(page, queryWrapper);

        return iPage.getRecords();
    }

    /**
     * 根据品牌Id集合获取品牌信息
     *
     * @param brandIds 品牌Id集合
     * @return 品牌信息
     */
    public List<PdsBrandPo> getBrandList(List<Integer> brandIds) {
        if (brandIds == null || brandIds.isEmpty()) {
            return new ArrayList<PdsBrandPo>();
        } else {
            return pdsBrandMapper.selectBatchIds(brandIds);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateLastSnCode(PdsBrand pdsBrand) {
        return pdsBrandRepository.updateLastSnCode(pdsBrand);
    }
}
