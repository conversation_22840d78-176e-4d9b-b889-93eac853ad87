package com.renpho.erp.pds.application.common.converter;

import com.renpho.erp.pds.application.product.manager.dto.*;
import com.renpho.erp.pds.application.product.manager.dto.cmd.PdsProductManagerSubmitCmd;
import com.renpho.erp.pds.domain.product.srm.request.ProductManagerBasicRequest;
import com.renpho.erp.pds.infrastructure.persistence.po.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PdsProductManagerPoConverter {

    /**
     * 实例
     */
    PdsProductManagerPoConverter INSTANCE = Mappers.getMapper(PdsProductManagerPoConverter.class);

    PdsProductManagerBasicPo basicRequestToBasicPo(PdsProductManagerUpdateBasicRequest basic);

    PdsProductManagerProsPo prosRequestToProsPo(PdsProductManagerUpdateProsRequest pros);

    PdsProductManagerProsBoxPo prosBoxRequestToProsBoxPo(PdsProductManagerUpdateProsBoxRequest box);

    PdsProductManagerLogisticsPo logisticsRequestToLogisticsPo(PdsProductManagerUpdateLogisticsRequest logistics);

    PdsProductManagerFittingPo fittingRequestToFittingPo(PdsProductManagerUpdateFittingRequest fitting);

    PurchaseSkuImportError toPurchaseSkuImportError(PurchaseSkuImport importData);

    PdsProductManagerSubmitRequest submitCmdToProductManagerSubmit(PdsProductManagerSubmitCmd submitCmd);

    ProductManagerBasicRequest toRequestMq(PdsProductManagerBasicPo basicPo);
}
