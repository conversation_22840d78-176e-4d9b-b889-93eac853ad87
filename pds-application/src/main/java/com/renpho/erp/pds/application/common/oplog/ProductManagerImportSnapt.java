package com.renpho.erp.pds.application.common.oplog;

import com.alibaba.fastjson.JSONObject;
import com.renpho.erp.oplog.log.SnapshotDatatSource;
import com.renpho.erp.pds.application.product.manager.service.PdsProductManagerSrv;
import com.renpho.erp.pds.domain.common.LanguageEnum;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsProductManagerDetailsResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 产品管理-日志,导入.
 *
 * <AUTHOR>
 * @since 2024.11.26
 */
@Service
public class ProductManagerImportSnapt implements SnapshotDatatSource {

    @Resource
    private PdsProductManagerSrv pdsProductManagerSrv;

    @Override
    public JSONObject getOldData(Object[] args) {
        return new JSONObject();
    }

    @Override
    public JSONObject getNewData(Object[] args, JSONObject result) {
        Object id =  result.get("data");
        if (id == null) {
           return new JSONObject();
        }
        PdsProductManagerDetailsResponse detail = pdsProductManagerSrv.details(Integer.parseInt(String.valueOf(id)), LanguageEnum.China);

        return (JSONObject) JSONObject.toJSON(detail);
    }

    @Override
    public String getBsId(Object[] args, JSONObject result) {
        Object id =  result.get("data");
        return String.valueOf(id);
    }
}
