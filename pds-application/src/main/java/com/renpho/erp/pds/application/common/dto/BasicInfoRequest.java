package com.renpho.erp.pds.application.common.dto;

import lombok.Data;

import java.util.List;

/**
 * 基础信息请求类-颜色\品类\品牌\国家地区.
 *
 * <AUTHOR>
 * @since 2024.12.06
 */
@Data
public class BasicInfoRequest {

    /**
     * 颜色ID集合
     */
    private List<Integer> colorIds;

    /**
     * 品类ID集合
     */
    private List<Integer> categoryIds;

    /**
     * 品牌ID集合
     */
    private List<Integer> brandIds;

    /**
     * 国家地区ID集合
     */
    private List<Integer> countryRegionIds;

    /**
     * 型号ID集合
     */
    private List<Integer> modelIds;

    /**
     * 0 英文， 1 中文
     */
    private Integer language = 1;

}
