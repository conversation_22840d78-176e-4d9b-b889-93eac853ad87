package com.renpho.erp.pds.application.common.oplog.module;

import com.alibaba.fastjson.JSONObject;
import com.renpho.erp.oplog.log.SnapshotDatatSource;
import com.renpho.erp.pds.application.common.oplog.model.ProductManagerAuthorizeModel;
import com.renpho.erp.pds.application.product.manager.authorize.service.PdsProductManagerAuthorizeSrv;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsProductManagerAuthorizePo;
import com.renpho.erp.pds.infrastructure.utils.LocalHashMapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 产品管理-授权-导入.
 *
 * <AUTHOR>
 * @since 2024.12.05
 */
@Service
public class ProductManagerAuthorizeImportSnapt implements SnapshotDatatSource {

    @Autowired
    private PdsProductManagerAuthorizeSrv pdsProductManagerAuthorizeSrv;

    @Override
    public JSONObject getOldData(Object[] args) {
        return new JSONObject();
    }

    @Override
    public JSONObject getNewData(Object[] args, JSONObject result) {
        ProductManagerAuthorizeModel mainCmd = (ProductManagerAuthorizeModel) args[0];
        if (mainCmd == null) {
            return new JSONObject();
        }
        PdsProductManagerAuthorizePo authorizePo = pdsProductManagerAuthorizeSrv.getPdsProductManagerAuthorizePo(mainCmd.getAuthorizeId());

        return (JSONObject) JSONObject.toJSON(authorizePo);
    }

    @Override
    public String getBsId(Object[] args, JSONObject result) {
        ProductManagerAuthorizeModel mainCmd = (ProductManagerAuthorizeModel) args[0];
        if ( mainCmd == null ) {
            return null;
        }
        else {
            LocalHashMapUtils.clear();
            return String.valueOf(mainCmd.getProductManagerId());
        }
    }
}
