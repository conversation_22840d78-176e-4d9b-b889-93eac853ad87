package com.renpho.erp.pds.application.common.service;

import com.renpho.erp.pds.application.brand.service.PdsBrandSrv;
import com.renpho.erp.pds.application.category.service.PdsCategorySrv;
import com.renpho.erp.pds.application.color.service.PdsColorSrv;
import com.renpho.erp.pds.application.common.dto.BasicInfoRequest;
import com.renpho.erp.pds.application.common.dto.BasicInfoResponse;
import com.renpho.erp.pds.application.country.service.PdsCountryRegionSrv;
import com.renpho.erp.pds.application.product.model.service.PdsProductModelSrv;
import com.renpho.erp.pds.domain.brand.PdsBrand;
import com.renpho.erp.pds.domain.color.LanguageColor;
import com.renpho.erp.pds.domain.common.LanguageEnum;
import com.renpho.erp.pds.domain.country.LanguageCountryRegion;
import com.renpho.erp.pds.domain.country.PdsCountryRegion;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsBrandPo;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsColorPo;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsCountryRegionPo;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsProductModelPo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * feign对外接口.
 *
 * <AUTHOR>
 * @since 2024.12.06
 */
@Slf4j
@Service
public class PdsViewSrv {

    @Resource
    private PdsBrandSrv brandSrv;

    @Resource
    private PdsColorSrv colorSrv;

    @Resource
    private PdsCategorySrv categorySrv;

    @Resource
    private PdsCountryRegionSrv countryRegionSrv;

    @Resource
    private PdsCommonSrv commonSrv;

    @Resource
    private PdsProductModelSrv productModelSrv;

    /**
     * 获取基础信息.
     *
     * @param request 请求对象
     * @return 基础信息
     */
    public BasicInfoResponse getBasicInfo(BasicInfoRequest request) {
        LanguageEnum language = LanguageEnum.getEnum(request.getLanguage());
        BasicInfoResponse response = new BasicInfoResponse();

        List<PdsBrandPo> brandList = brandSrv.getBrandList(request.getBrandIds());
        for (PdsBrandPo brandPo : brandList) {
            String name = brandPo.getBrand();
            response.addBrand(brandPo.getId(), name);
        }

        List<PdsColorPo> colorList = colorSrv.getColorList(request.getColorIds());
        for (PdsColorPo colorPo : colorList) {
            List<LanguageColor> languageList = colorSrv.getLanguages(colorPo.getId());
            String name = LanguageEnum.getLanguage(languageList, language);
            response.addColor(colorPo.getId(), name);
        }

        for(Integer id : request.getCategoryIds()){
            StringBuilder sb = new StringBuilder();
            categorySrv.getCategoryTreeName(id,language, sb);
            response.addCategory(id, sb.toString());
        }

        List<PdsProductModelPo> modelList = productModelSrv.getModelList(request.getModelIds());
        for(PdsProductModelPo modelPo : modelList){
            response.addModel(modelPo.getId(), modelPo.getModelNo());
        }

        List<PdsCountryRegionPo> countryRegionList = countryRegionSrv.getCountryRegionList(request.getCountryRegionIds());
        for (PdsCountryRegionPo countryRegionPo : countryRegionList) {
            List<LanguageCountryRegion>  languageList = countryRegionSrv.getLanguages(countryRegionPo.getId());
            String name = LanguageEnum.getLanguage(languageList, language);
            response.addCountryRegion(countryRegionPo.getId(), name);
        }

        return response;
    }
}
