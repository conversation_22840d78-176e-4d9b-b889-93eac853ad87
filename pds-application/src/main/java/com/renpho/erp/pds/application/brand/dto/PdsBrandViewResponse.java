package com.renpho.erp.pds.application.brand.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 品牌对外.
 * <AUTHOR>
 * @since 2024.9.29
 */
@Data
public class PdsBrandViewResponse {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 品牌代码
     */
    private String code;

    /**
     * 注册实体
     */
    private String registeredEntity;

    /**
     * 注册时间
     */
    private LocalDateTime registeredDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * Menu Status 选项为 Active 和 Inactive 单选，默认选中Active
     */
    private Integer status;

}