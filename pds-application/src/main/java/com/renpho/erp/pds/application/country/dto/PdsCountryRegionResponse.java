package com.renpho.erp.pds.application.country.dto;

import com.renpho.erp.pds.application.common.dto.CommonResponse;
import com.renpho.erp.pds.domain.common.LanguageEnum;
import com.renpho.erp.pds.domain.country.LanguageCountryRegion;
import lombok.Data;

import java.util.List;

/**
 * 国家区域表.
 *
 * <AUTHOR>
 * @since 2024.9.24
 */
@Data
public class PdsCountryRegionResponse extends CommonResponse {

    /**
     * 主键, 添加不需要，更新才需要
     */
    private Integer id;

    /**
     * 国家区域代码
     */
    private String code;

    /**
     * 备注
     */
    private String remark;

    /**
     * Menu Status 选项为 Active 和 Inactive 单选，默认选中Active
     */
    private Integer status;

    /**
     * 多语言
     */
    private List<LanguageCountryRegion> names;

    /**
     * 名称，解析完多语言
     */
    private String name;

    public void initLanguageName(LanguageEnum language) {
        if (language == null) {
            language = LanguageEnum.China;
        }
        this.name = getLanguage(language);
    }

    /**
     * 根据语言环境获取语言信息
     *
     * @param language 语言
     * @return 语言信息
     */
    public String getLanguage(LanguageEnum language) {
        if (names == null || names.isEmpty()) {
            return null;
        }
        if (LanguageEnum.China == language) {
            for (LanguageCountryRegion languageData : names) {
                if (languageData.getLanguage().equals("zh-CN")) {
                    return languageData.getName();
                }
            }
        } else {
            for (LanguageCountryRegion languageData : names) {
                if (languageData.getLanguage().equals("en-US")) {
                    return languageData.getName();
                }
            }
        }
        return null;
    }

}
