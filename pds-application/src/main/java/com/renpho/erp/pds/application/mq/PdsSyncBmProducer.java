package com.renpho.erp.pds.application.mq;

import com.renpho.erp.pds.domain.common.MQConstants;
import com.renpho.erp.pds.domain.product.srm.request.ProductManagerBasicRequest;
import com.renpho.erp.stream.StreamCommonConstants;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * 推送产品管理基础信息到产品商务管理的生产者
 * <AUTHOR>
 * @since 2024/12/10
 */
@Component
@RequiredArgsConstructor
public class PdsSyncBmProducer {

	private final StreamBridge streamBridge;

	/**
	 * 推送产品管理基础信息到SRM产品商务管理的生产者
	 * @param request 推送的参数
	 */
	public void send(ProductManagerBasicRequest request) {
		streamBridge.send(MQConstants.PDS_PUSH_BUSINESS_TOPIC, MessageBuilder.withPayload(request)
				.setHeader(StreamCommonConstants.HEADER_TAGS, request.getProductStatus()).build());
	}

}
