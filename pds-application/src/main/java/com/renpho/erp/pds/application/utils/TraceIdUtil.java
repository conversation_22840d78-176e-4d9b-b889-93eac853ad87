package com.renpho.erp.pds.application.utils;

import org.slf4j.MDC;

import java.util.UUID;

/**
 * 链路ID工具类
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
public class TraceIdUtil {
    private static final String TRACE_ID_KEY = "tlogTraceId";

    // 生成链路 ID
    public static String generateTraceId() {
        return UUID.randomUUID().toString();
    }

    // 设置链路 ID 到 MDC
    public static void setTraceId(String traceId) {
        MDC.put(TRACE_ID_KEY, traceId);
    }

    // 获取当前链路 ID
    public static String getTraceId() {
        return MDC.get(TRACE_ID_KEY);
    }

    // 清理 MDC
    public static void clear() {
        MDC.remove(TRACE_ID_KEY);
    }
}

