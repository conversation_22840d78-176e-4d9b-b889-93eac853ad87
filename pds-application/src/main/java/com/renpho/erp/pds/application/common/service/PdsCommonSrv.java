package com.renpho.erp.pds.application.common.service;

import com.alibaba.fastjson.JSON;
import com.renpho.erp.data.trans.MultiLanguage;
import com.renpho.erp.data.trans.dict.Dict;
import com.renpho.erp.data.trans.kit.DictKits;
import com.renpho.erp.data.trans.kit.SystemParamKits;
import com.renpho.erp.ftm.client.request.FileRequest;
import com.renpho.erp.ftm.client.response.FileDetailResponse;
import com.renpho.erp.ftm.client.response.FileInfoResponse;
import com.renpho.erp.mdm.client.saleschannel.RemoteSalesChannelService;
import com.renpho.erp.mdm.client.saleschannel.command.SalesChannelCodesQuery;
import com.renpho.erp.mdm.client.saleschannel.vo.SalesChannelVo;
import com.renpho.erp.pds.application.common.dto.CommonResponse;
import com.renpho.erp.pds.domain.common.CommonConstants;
import com.renpho.erp.pds.domain.common.file.FileUploadType;
import com.renpho.erp.pds.infrastructure.exception.PdsErrorCode;
import com.renpho.erp.pds.infrastructure.feignclient.RemoteFileFeign;
import com.renpho.erp.pds.infrastructure.feignclient.RemoteUserDetailsFeign;
import com.renpho.erp.smc.client.dto.OumLabelUserSimpleVo;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import com.renpho.erp.smc.client.dto.QueryOumLabelUserCmd;
import com.renpho.erp.smc.client.dto.QueryUserListReq;
import com.renpho.karma.dto.R;
import com.renpho.karma.exception.ErrorCodeException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 公共服务接口.
 *
 * <AUTHOR>
 * @since 2024.9.24
 */
@Slf4j
@Service
public class PdsCommonSrv {

    @Resource
    private RemoteUserDetailsFeign remoteUserDetailsFeign;

    @Resource
    private RemoteFileFeign remoteFileFeign;

    @Resource
    private RemoteSalesChannelService remoteSalesChannelService;

    /**
     * 补充用户信息
     *
     * @param commonResponseList 列表返回公共数据
     */
    public void initUsers(List<? extends CommonResponse> commonResponseList) {
        Set<Integer> userIds = new HashSet<>();
        for (CommonResponse common : commonResponseList) {
            if (common.getCreateBy() != null) {
                userIds.add(common.getCreateBy());
            }
            if (common.getUpdateBy() != null) {
                userIds.add(common.getUpdateBy());
            }
        }
        Map<Integer, OumUserInfoRes> users = new HashMap<>();
        List<OumUserInfoRes> usersResult = this.getUserListInfo(userIds.stream().toList());
        if (usersResult != null) {
            for (OumUserInfoRes user : usersResult) {
                users.put(user.getId(), user);
            }
        }
        if (!users.isEmpty()) {
            for (CommonResponse common : commonResponseList) {
                common.initUserInfo(users);
            }
        }
    }

    /**
     * 根据用户Id集合获取用户集合信息
     *
     * @param userIdList 用户Id集合
     * @return 用户集合信息
     */
    public List<OumUserInfoRes> getUserListInfo(List<Integer> userIdList) {
        if (userIdList == null || userIdList.isEmpty()) {
            return new ArrayList<OumUserInfoRes>();
        }

        QueryUserListReq req = new QueryUserListReq();
        req.setUserIds(userIdList);
        log.info("请求【获取用户列表】接口，req：{}", JSON.toJSON(req));
        R<List<OumUserInfoRes>> resp = remoteUserDetailsFeign.getUserListInfo(req);
        log.info("请求【获取用户列表】接口，resp：{}", JSON.toJSON(resp));
        if (!resp.isSuccess()) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCTMODEL_GETUSERLIST_FAIL);
        }
        List<OumUserInfoRes> oumUserInfoResList = resp.getData();
        return oumUserInfoResList;
    }

    /**
     * 根据用户Id集合获取用户集合信息
     *
     * @param userIdList 用户Id集合
     * @return 用户集合信息
     */
    public Map<Integer, OumUserInfoRes> getUserMap(List<Integer> userIdList) {
        List<OumUserInfoRes> users = getUserListInfo(userIdList);
        if (users == null || users.isEmpty()) {
            return new HashMap<>();
        }

        Map<Integer, OumUserInfoRes> userMap = new HashMap<>();
        for (OumUserInfoRes user : users) {
            userMap.put(user.getId(), user);
        }
        return userMap;
    }

    /**
     * 根据工号查询用户信息
     *
     * @param userCode 工号
     * @return 用户信息
     */
    public OumUserInfoRes getUserInfo(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return null;
        }
        QueryUserListReq req = new QueryUserListReq();
        req.setCode(userCode);
        log.info("请求【获取用户列表】接口，req：{}", JSON.toJSON(req));
        R<List<OumUserInfoRes>> resp = remoteUserDetailsFeign.getUserListInfo(req);
        log.info("请求【获取用户列表】接口，resp：{}", JSON.toJSON(resp));
        if (!resp.isSuccess()) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCTMODEL_GETUSERLIST_FAIL);
        }
        List<OumUserInfoRes> oumUserInfoResList = resp.getData();
        if (oumUserInfoResList != null && !oumUserInfoResList.isEmpty()) {
            return oumUserInfoResList.get(0);
        }
        return null;
    }

    /**
     * 根据id查询用户信息
     *
     * @param userId id
     * @return 用户信息
     */
    public OumUserInfoRes getUserInfoById(Integer userId) {
        if (userId == null) {
            return null;
        }
        QueryUserListReq req = new QueryUserListReq();
        req.setUserIds(Collections.singletonList(userId));
        log.info("请求【获取用户列表】接口，req：{}", JSON.toJSON(req));
        R<List<OumUserInfoRes>> resp = remoteUserDetailsFeign.getUserListInfo(req);
        log.info("请求【获取用户列表】接口，resp：{}", JSON.toJSON(resp));
        if (!resp.isSuccess()) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCTMODEL_GETUSERLIST_FAIL);
        }
        List<OumUserInfoRes> oumUserInfoResList = resp.getData();
        if (oumUserInfoResList != null && !oumUserInfoResList.isEmpty()) {
            return oumUserInfoResList.get(0);
        }
        return null;
    }

    /**
     * 获取用户描述
     *
     * @param userInfo 用户信息
     * @return 用户描述
     */
    public String getUserDesc(OumUserInfoRes userInfo) {
        return userInfo.getName() + " (" + userInfo.getCode() + ")";
    }

    /**
     * 根据工号集合查询用户信息
     * @param userCodes 工号集合
     * @return 用户信息
     */
    public Map<String, OumUserInfoRes> getUserMapByUserCodes(List<String> userCodes) {
        Map<String, OumUserInfoRes> userMap = new HashMap<>();
        if (userCodes == null || userCodes.isEmpty()) {
            return userMap;
        }
        QueryUserListReq req = new QueryUserListReq();
        req.setCodes(userCodes);
        log.info("请求【获取用户列表】接口，req：{}", JSON.toJSON(req));
        R<List<OumUserInfoRes>> resp = remoteUserDetailsFeign.getUserListInfo(req);
        log.info("请求【获取用户列表】接口，resp：{}", JSON.toJSON(resp));
        if (!resp.isSuccess()) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCTMODEL_GETUSERLIST_FAIL);
        }
        List<OumUserInfoRes> oumUserInfoResList = resp.getData();
        if (oumUserInfoResList != null && !oumUserInfoResList.isEmpty()) {
            for (OumUserInfoRes user : oumUserInfoResList) {
                userMap.put(user.getCode(), user);
            }
        }
        return userMap;
    }

    /**
     * 根据字典类型获取字典
     * @param type 字典类型
     * @return 字典
     */
    public List<Dict.ParamItem> getDict(String type){
        Dict productTypeDict = DictKits.getValue(() -> type);
        if (productTypeDict == null) {
            return null;
        }
        List<Dict.ParamItem> items = productTypeDict.getParams();
        if (items == null || items.isEmpty()) {
            return null;
        }
        return items;
    }

    /**
     * 根据字典值获取字典键值
     * @param value 字典值
     * @param items 字典
     * @return 字典键值
     */
    public String getDictValue(String value, List<Dict.ParamItem> items) {
        for (Dict.ParamItem item : items) {
            List<MultiLanguage> languages = item.getNames();
            for (MultiLanguage language : languages) {
                if (StringUtils.equals(language.getName(), value)) {
                    return item.getValue();
                }
            }
        }
        return null;
    }

    /**
     * 根据字典key判断字典是否包含该key
     * @param key 字典key
     * @param items 字典
     * @return 是否包含
     */
    public boolean containDictKey(String key, List<Dict.ParamItem> items) {
        for (Dict.ParamItem item : items) {
            if (StringUtils.equals(item.getValue(), key)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据字典类型和字典值获取字典key
     * @param type 字典类型
     * @param value 字典值
     * @return 字典key
     */
    public String getDictKeyByTypeAndValue(String type, String value) {
        Dict productTypeDict = DictKits.getValue(() -> type);
        if (productTypeDict == null) {
            return null;
        }
        List<Dict.ParamItem> items = productTypeDict.getParams();
        if (items == null || items.isEmpty()) {
            return null;
        }
        for (Dict.ParamItem item : items) {
            List<MultiLanguage> languages = item.getNames();
            for (MultiLanguage language : languages) {
                if (StringUtils.equals(language.getName(), value)) {
                    return item.getValue();
                }
            }
        }
        return null;
    }

    /**
     * 根据文件ID获取文件信息
     * @param fileId 文件ID
     * @return 文件信息
     */
    public FileDetailResponse getFileInfo(String fileId){
        if(StringUtils.isBlank(fileId)){
            return null;
        }
        FileRequest cmd = new FileRequest();
        cmd.setFileIds(Collections.singletonList(fileId));
        cmd.setContentDisposition("inline;");
        log.info("请求【获取文件列表】接口，req：{}", JSON.toJSON(cmd));
        R<FileDetailResponse> responseR = remoteFileFeign.getFileInfo(cmd);
        if (responseR.isSuccess()) {
            return responseR.getData();
        }
        return null;
    }

    /**
     * 根据文件ID获取文件信息集合
     * @param fileIdList 文件ID集合
     * @return 文件信息集合
     */
    public List<FileDetailResponse> getFileInfoList(List<String> fileIdList){
        if(fileIdList==null || fileIdList.isEmpty()){
            return null;
        }
        FileRequest cmd = new FileRequest();
        cmd.setFileIds(fileIdList);
        cmd.setContentDisposition("inline;");
        log.info("请求【获取文件列表】接口，req：{}", JSON.toJSON(cmd));
        R<List<FileDetailResponse>> responseR = remoteFileFeign.getFileInfoList(cmd);
        if (responseR.isSuccess()) {
            return responseR.getData();
        }
        return null;
    }

    /**
     * 上传图片接口
     * @param imageUrl 图片地址
     * @param fileUploadType 文件上传类型
     * @param modules 模块
     * @return 文件信息
     */
    public FileInfoResponse uploadFileByUrl(String imageUrl, FileUploadType fileUploadType, String modules){
        if (StringUtils.isBlank(imageUrl)) {
            return null;
        }
        // 验证图片,后面通过批量接口处理
        List<String> fileUrls = List.of(imageUrl);
        R<List<FileInfoResponse>> resp = remoteFileFeign.uploadByUrl(fileUrls, fileUploadType.getCode(), RemoteFileFeign.UPLOAD_SYSTEM, modules, true);
        if (!resp.isSuccess()) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCTMODEL_UPLOAD_FAIL);
        }
        if (resp.getData() == null || resp.getData().isEmpty()) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCTMODEL_UPLOAD_FAIL);
        }
        return  resp.getData().get(0);
    }

    /**
     * 上传图片接口
     * @param imageUrls 图片地址
     * @param fileUploadType 文件上传类型
     * @param modules 模块
     * @return 文件信息
     */
    public List<FileInfoResponse> uploadFileByUrls(List<String> imageUrls, FileUploadType fileUploadType, String modules){
        if (imageUrls==null || imageUrls.isEmpty()) {
            return null;
        }
        R<List<FileInfoResponse>> resp = remoteFileFeign.uploadByUrl(imageUrls, fileUploadType.getCode(), RemoteFileFeign.UPLOAD_SYSTEM, modules, true);
        if (!resp.isSuccess()) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCTMODEL_UPLOAD_FAIL);
        }
        if (resp.getData() == null || resp.getData().isEmpty()) {
            throw new ErrorCodeException(PdsErrorCode.PRODUCTMODEL_UPLOAD_FAIL);
        }
        return  resp.getData();
    }

    /**
     * 根据销售渠道编码查询销售渠道信息
     * @param salesChannelCodeList 销售渠道编码列表
     * @return 销售渠道信息列表
     */
    public List<SalesChannelVo> getSalesChannelList(List<String> salesChannelCodeList) {
        if (salesChannelCodeList == null || salesChannelCodeList.isEmpty()) {
            return new ArrayList<>();
        }
        SalesChannelCodesQuery request = new SalesChannelCodesQuery();
        request.setCodes(salesChannelCodeList);
        R<List<SalesChannelVo>> salesChannelCodeResp = remoteSalesChannelService.findByCodes(request);
        if (!salesChannelCodeResp.isSuccess()) {
            throw new RuntimeException("销售渠道接口调用失败");
        }
        if (salesChannelCodeResp.getData().isEmpty()) {
            throw new RuntimeException("销售渠道不存在");
        }
        return salesChannelCodeResp.getData();
    }

    /**
     * 获取标签用户列表(当前用户的下级列表)
     * @param userId 用户ID
     * @param labelId 标签ID
     * @return 标签用户列表
     */
    public List<OumLabelUserSimpleVo> getLabelUserList(Integer userId, String labelId) {
        if (userId == null || StringUtils.isBlank(labelId)) {
            return new ArrayList<>();
        }

        QueryOumLabelUserCmd cmd = new QueryOumLabelUserCmd();
        cmd.setUserId(userId);
        cmd.setStatus(String.valueOf(CommonConstants.ACTIVE));
        cmd.setRoleLabel(labelId);
        log.info("请求【获取标签用户列表】接口，req：{}", JSON.toJSON(cmd));
        R<List<OumLabelUserSimpleVo>> datas = this.remoteUserDetailsFeign.getLabelSimpleList(cmd);
        log.info("请求【获取标签用户列表】接口，res：{}", JSON.toJSON(datas));
        if (!datas.isSuccess()) {
            throw new RuntimeException("标签用户查询失败");
        }
        return datas.getData();
    }

    /**
     * 获取标签用户列表(当前用户的下级列表)
     * @param userId 用户ID
     * @param labelId 标签ID
     * @return 标签用户map -> 工号:用户信息
     */
    public Map<String,OumLabelUserSimpleVo> getLabelUserMap(Integer userId, String labelId) {
        if (userId == null || StringUtils.isBlank(labelId)) {
            return new HashMap<>();
        }
        List<OumLabelUserSimpleVo> labelUserList = this.getLabelUserList(userId, labelId);
        if (labelUserList == null || labelUserList.isEmpty()) {
            return new HashMap<>();
        }
        Map<String,OumLabelUserSimpleVo> labelUserMap = new HashMap<>();
        for (OumLabelUserSimpleVo labelUser : labelUserList) {
            labelUserMap.put(labelUser.getCode(), labelUser);
        }
        return labelUserMap;
    }

    /**
     * 查询系统参数值
     * @param value 字典值
     * @param items 字典
     * @return 字典键值
     */
    public String getSystemValue(String value) {
        return SystemParamKits.getValue(value);
    }

}
