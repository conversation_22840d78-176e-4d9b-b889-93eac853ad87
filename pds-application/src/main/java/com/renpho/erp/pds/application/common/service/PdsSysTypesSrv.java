package com.renpho.erp.pds.application.common.service;

import com.renpho.erp.pds.domain.common.PdsSysTypes;
import com.renpho.erp.pds.domain.common.SysModuleEnum;
import com.renpho.erp.pds.infrastructure.covert.PdsSysTypeTransformer;
import com.renpho.erp.pds.infrastructure.exception.PdsErrorCode;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsSysTypesRequest;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsSysTypesResponse;
import com.renpho.erp.pds.infrastructure.persistence.mapper.PdsSysTypesMapper;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsSysTypesPo;
import com.renpho.karma.exception.ErrorCodeException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 自定义类型接口.
 *
 * <AUTHOR>
 * @since 2024.11.18
 */
@Slf4j
@Service
public class PdsSysTypesSrv {

    @Resource
    private PdsSysTypesMapper sysTypesMapper;

    /**
     * 查询系统类型列表。
     *
     * @param moduleName 模块名称
     * @param typeName   类型名称
     * @param typeValue  类型值
     * @return 系统类型列表
     */
    public List<PdsSysTypesResponse> list(String moduleName, String typeName, String typeValue) {
        return sysTypesMapper.list(moduleName, typeName, typeValue);
    }

    /**
     * 添加系统类型。
     *
     * @param request 系统类型请求对象
     * @return 是否添加成功
     */
    public boolean add(PdsSysTypesRequest request) {
        PdsSysTypesPo po = new PdsSysTypesPo();
        po.setModuleName(request.getModuleName());
        po.setTypeName(request.getTypeName());
        po.setTypeValue(request.getTypeValue());

        Integer count = sysTypesMapper.selectSysTypeCount(request.getModuleName(), request.getTypeName(), request.getTypeValue());
        if (count > 0) {
            throw new ErrorCodeException(PdsErrorCode.SYS_TYPE_EXISTS);
        }
        return sysTypesMapper.insert(po) > 0;
    }

    /**
     * 尝试添加系统类型。
     *
     * @param sysModule 模块名称
     * @param typeName   类型名称, 见{SysTypeConstants}
     * @param typeValue  类型值
     */
    @Transactional(rollbackFor = Exception.class)
    public PdsSysTypes saveType(SysModuleEnum sysModule, String typeName, String typeValue) {
        if (sysModule == null || StringUtils.isBlank(typeName) || StringUtils.isBlank(typeValue)) {
            return null;
        }
        List<PdsSysTypesPo> pdsSysTypesPos = sysTypesMapper.selectListSysTypeCount(sysModule.getValue(), typeName, typeValue);
        if (CollectionUtils.isNotEmpty(pdsSysTypesPos)){
            PdsSysTypesPo pdsSysTypesPo = pdsSysTypesPos.stream().findFirst().get();
            return PdsSysTypeTransformer.INSTANCE.toDomain(pdsSysTypesPo);
        }
        PdsSysTypesPo po = new PdsSysTypesPo();
        po.setModuleName(sysModule.getValue());
        po.setTypeName(typeName);
        po.setTypeValue(typeValue);

        sysTypesMapper.insert(po);
        return PdsSysTypeTransformer.INSTANCE.toDomain(po);
    }

    /**
     * 更新系统类型。
     *
     * @param request 系统类型请求对象
     * @return 是否更新成功
     */
    public boolean update(PdsSysTypesRequest request) {
        PdsSysTypesPo po = new PdsSysTypesPo();
        po.setModuleName(request.getModuleName());
        po.setTypeName(request.getTypeName());
        po.setTypeValue(request.getTypeValue());
        return sysTypesMapper.updateById(po) > 0;  //分析一下，是不是不为null才更新
    }

}
