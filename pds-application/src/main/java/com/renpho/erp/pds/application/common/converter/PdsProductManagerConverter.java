package com.renpho.erp.pds.application.common.converter;

import com.renpho.erp.pds.application.product.manager.certification.dto.*;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsProductManagerBasicViewResponse;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsProductManagerCertificationBasicResponse;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsProductManagerCertificationComponentResponse;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsProductManagerCertificationMachineResponse;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsProductManagerBasicPo;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsProductManagerCertificationBasicPo;

import com.renpho.erp.pds.infrastructure.persistence.po.PdsProductManagerCertificationComponentPo;
import com.renpho.erp.pds.infrastructure.persistence.po.PdsProductManagerCertificationMachinePo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 产品管理-数据转换.
 *
 * <AUTHOR>
 * @since 2024.11.19
 */
@Mapper
public interface PdsProductManagerConverter {

    /**
     * 转换器实例
     */
    PdsProductManagerConverter INSTANCE = Mappers.getMapper(PdsProductManagerConverter.class);

    PdsProductManagerCertificationBasicResponse toBasicResponse(PdsProductManagerCertificationBasicPo basicPo);

    List<PdsProductManagerCertificationMachineResponse> toMachineList(List<PdsProductManagerCertificationMachinePo> machinePoList);

    List<PdsProductManagerCertificationComponentResponse> toComponentList(List<PdsProductManagerCertificationComponentPo> componentPoList);

    PdsProductManagerCertificationBasicPo toBasicPo(PdsProductManagerUpdateCertificationBasicRequest certificationBasic);

    PdsProductManagerCertificationMachinePo toMachinePo(PdsProductManagerUpdateCertificationMachineRequest machineRequest);

    PdsProductManagerCertificationComponentPo toComponentPo(PdsProductManagerUpdateCertificationComponentRequest componentRequest);

    PdsProductManagerBasicViewResponse toBasicViewResponse(PdsProductManagerBasicPo basicPo);
}
