package com.renpho.erp.pds.application.common.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 基础信息请求类-颜色\品类\品牌\国家地区.
 *
 * <AUTHOR>
 * @since 2024.12.06
 */
@Data
public class BasicInfoResponse {

    private List<Mapping> colors = new ArrayList<Mapping>();

    private List<Mapping> categories = new ArrayList<Mapping>();

    private List<Mapping> brands = new ArrayList<Mapping>();

    private List<Mapping> countryRegions = new ArrayList<Mapping>();

    private List<Mapping> models = new ArrayList<Mapping>();

    public void addColor(Integer id, String name) {
        colors.add(new Mapping(id, name));
    }

    public void addCategory(Integer id, String name) {
        categories.add(new Mapping(id, name));
    }

    public void addBrand(Integer id, String name) {
        brands.add(new Mapping(id, name));
    }

    public void addCountryRegion(Integer id, String name) {
        countryRegions.add(new Mapping(id, name));
    }

    public void addModel(Integer id, String modelNo) {
        models.add(new Mapping(id, modelNo));
    }

    // Inner class to define Mapping
    public static class Mapping {
        private Integer id;
        private String name;

        public Mapping(Integer id, String name) {
            this.id = id;
            this.name = name;
        }

        // Getters and Setters
        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    // Getters and Setters
    public List<Mapping> getColors() {
        return colors;
    }

    public void setColors(List<Mapping> colors) {
        this.colors = colors;
    }

    public List<Mapping> getCategories() {
        return categories;
    }

    public void setCategories(List<Mapping> categories) {
        this.categories = categories;
    }

    public List<Mapping> getBrands() {
        return brands;
    }

    public void setBrands(List<Mapping> brands) {
        this.brands = brands;
    }

    public List<Mapping> getCountryRegions() {
        return countryRegions;
    }

    public void setCountryRegions(List<Mapping> countryRegions) {
        this.countryRegions = countryRegions;
    }
}

