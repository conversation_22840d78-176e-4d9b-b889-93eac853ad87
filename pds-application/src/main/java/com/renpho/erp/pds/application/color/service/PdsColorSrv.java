package com.renpho.erp.pds.application.color.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.renpho.erp.pds.domain.color.LanguageColor;
import com.renpho.erp.pds.domain.color.PdsColor;
import com.renpho.erp.pds.domain.color.PdsColorRepository;

import com.renpho.erp.pds.domain.common.LanguageRepository;
import com.renpho.erp.pds.infrastructure.persistence.dto.CodeResponse;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsColorRequest;
import com.renpho.erp.pds.infrastructure.persistence.mapper.PdsColorMapper;

import com.renpho.erp.pds.infrastructure.persistence.po.PdsColorPo;
import com.renpho.erp.pds.infrastructure.utils.PagingUtils;
import com.renpho.karma.dto.Paging;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 颜色服务接口.
 *
 * <AUTHOR>
 * @since 2024.9.19
 */
@Service
public class PdsColorSrv {

    @Autowired
    private PdsColorMapper pdsColorMapper;

    @Autowired
    private PdsColorRepository pdsColorRepository;

    @Autowired
    private LanguageRepository<LanguageColor> languageRepository;

    // 多语言表名
    private static final String L_NAME = "pds_color_language";

    /**
     * 添加/更新 多语言信息
     *
     * @param languages 语言信息
     * @param pk        主键
     * @param add       是否添加
     */
    public void saveLanguages(PdsColor languages, Integer pk, boolean add) {
        // 多语言
        List<LanguageColor> names = languages.getNames();
        if (names != null && !names.isEmpty()) {
            for (LanguageColor language : names) {
                language.setColorId(pk);
                if (add) {
                    languageRepository.insertLanguage(language, L_NAME);
                } else {
                    languageRepository.updateLanguage(language, L_NAME);
                }
            }
        }
    }

    /**
     * 查询多语言信息
     *
     * @param colorId 主键ID
     * @return 多语言信息
     */
    public List<LanguageColor> getLanguages(Integer colorId) {
        return languageRepository.selectByParentIdAndLanguage(colorId, L_NAME, LanguageColor.class);
    }

    /**
     * 添加颜色数据
     *
     * @param pdsColor 颜色数据
     * @return 主键Id
     */
    @Transactional
    public int add(PdsColor pdsColor) {
        Integer pk = pdsColorRepository.savePdsColor(pdsColor);
        // 多语言
        saveLanguages(pdsColor, pk, true);

        return pk;
    }

    /**
     * 跟新颜色数据
     *
     * @param pdsColor 颜色数据
     * @return 受影响条数
     */
    @Transactional
    public int update(PdsColor pdsColor) {
        // 多语言
        saveLanguages(pdsColor, pdsColor.getId().getId(), false);

        return pdsColorRepository.updatePdsColor(pdsColor);
    }

    /**
     * 禁用、启用
     *
     * @param pdsColor 状态数据
     * @return 受影响条数
     */
    public int changeStatus(PdsColor pdsColor) {
        return pdsColorRepository.updatePdsColor(pdsColor);
    }

    /**
     * 列表分页查询,单表
     *
     * @param pdsColorRequest 查询参数
     * @return 结果
     */
    public Paging<PdsColorPo> list(PdsColorRequest pdsColorRequest) {
        // 使用 PageHelper 进行分页
        int pageNum = pdsColorRequest.getPageIndex();
        int pageSize = pdsColorRequest.getPageSize();
        PageHelper.startPage(pageNum, pageSize);

        List<PdsColorPo> colors = pdsColorMapper.selectColors(pdsColorRequest.getKeyword());
        return PagingUtils.toPaging(new PageInfo<>(colors));
    }

    public PdsColorPo getPdsColorDetail(Integer id) {
        return this.pdsColorMapper.selectById(id);
    }

    /**
     * 批量查询
     * <p>
     * <br/> 多语言：List<LanguageColor> names = PdsColorSrv.getLanguages(po.getId());
     *
     * @param ids 主键ID集合
     * @return 结果
     */
    public List<PdsColorPo> getPdsColorDetails(List<Integer> ids) {
        if (ids != null && !ids.isEmpty()) {
            return this.pdsColorMapper.selectBatchIds(ids);
        }

        return new ArrayList<PdsColorPo>();
    }

    /**
     * 根据颜色编码获取颜色ID
     * @param code 颜色编码
     * @return 颜色ID
     */
    public Integer getColorIdByCode(String code) {
        return pdsColorMapper.getColorIdByCode(code);
    }

    /**
     * 根据颜色编码获取颜色ID
     * @param colorCodeBasicList 颜色编码集合
     * @return 颜色编码:颜色ID
     */
    public Map<String, Integer> getColorMap(List<String> colorCodeBasicList) {
        Map<String, Integer> map = new HashMap<String, Integer>();
        List<CodeResponse> codes = pdsColorMapper.getColorMap(colorCodeBasicList);
        for (CodeResponse code : codes) {
            map.put(code.getCode(), code.getId());
        }
        return map;
    }

    /**
     * 根据颜色ID集合查询颜色信息
     * @param colorIds 颜色ID集合
     * @return 颜色信息
     */
    public List<PdsColorPo> getColorList(List<Integer> colorIds) {
        if (colorIds != null && !colorIds.isEmpty()) {
            return this.pdsColorMapper.selectBatchIds(colorIds);
        }
        return new ArrayList<PdsColorPo>();
    }
}
