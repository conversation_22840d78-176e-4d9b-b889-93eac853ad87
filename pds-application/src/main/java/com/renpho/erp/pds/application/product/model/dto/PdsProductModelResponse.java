package com.renpho.erp.pds.application.product.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.renpho.karma.timezone.annotation.TimeZoneIgnore;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 产品型号表.
 */
@Data
public class PdsProductModelResponse {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 产品型号
     */
    private String modelNo;

    /**
     * 品牌主键ID
     */
    private Integer brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 一级品类主键ID
     */
    private Integer cateFirst;
    /**
     * 一级品类名称
     */
    private String cateFirstName;
    /**
     * 一级品类编码
     */
    private String cateFirstCode;

    /**
     * 二级品类主键ID
     */
    private Integer cateSecond;
    /**
     * 二级品类名称
     */
    private String cateSecondName;
    /**
     * 二级品类编码
     */
    private String cateSecondCode;

    /**
     * 三级品类主键ID
     */
    private Integer cateThird;
    /**
     * 三级品类名称
     */
    private String cateThirdName;
    /**
     * 三级品类编码
     */
    private String cateThirdCode;

    /**
     * 产品经理userid
     */
    private Integer pmUserid;

    /**
     * 产品经理姓名
     */
    private String pmUserName;

    /**
     * 产品经理工号
     */
    private String pmUserCode;

    /**
     * 操作人ID
     */
    private Integer updateBy;

    /**
     * 操作人姓名
     */
    private String updateByName;

    /**
     * 操作人工号
     */
    private String updateByCode;

    /**
     * 更新时间
     */
    @TimeZoneIgnore()
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 是否自动生成产品型号（0：否，1：是）
     */
    private Integer isAutoGenerated;
}