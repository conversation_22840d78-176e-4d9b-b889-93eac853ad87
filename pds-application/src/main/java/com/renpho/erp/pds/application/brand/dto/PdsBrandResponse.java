package com.renpho.erp.pds.application.brand.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.renpho.erp.pds.application.common.dto.CommonResponse;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 品牌表.
 * <AUTHOR>
 * @since 2024.9.14
 */
@Data
public class PdsBrandResponse extends CommonResponse {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 品牌代码
     */
    private String code;

    /**
     * 注册实体
     */
    private String registeredEntity;


    /**
     * 最后序列号：该品牌已使用到的SN码
     */
    private String lastSnCode;


    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date registeredDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * Menu Status 选项为 Active 和 Inactive 单选，默认选中Active
     */
    private Integer status;

    /**
     * 这个属性用于存储店铺ID列表
     */
    private List<Integer> storeIds;

    /**
     * 关联店铺数据
     */
    private List<PdsBrandStoresResponse> storesList;


    /**
     * 补充店铺信息
     * @param storeNames 店铺名称集合
     */
    public void initStoreInfo(HashMap<Integer, String> storeNames) {
        if (storeNames != null) {
            storesList = new ArrayList<PdsBrandStoresResponse>();
            for (Integer storeId : storeIds) {
                 PdsBrandStoresResponse store = new PdsBrandStoresResponse();
                 store.setStoreId(storeId);
                 store.setStoreName(storeNames.get(storeId));
                 storesList.add(store);
            }
        }
    }
}
