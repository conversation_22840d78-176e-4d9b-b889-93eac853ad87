package com.renpho.erp.pds.application.common.converter;

import com.renpho.erp.ftm.client.response.FileDetailResponse;
import com.renpho.erp.pds.domain.common.FtmFileDetailResponse;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface FileDatailConverter {



    /**
     * 实例
     */
    FileDatailConverter INSTANCE = Mappers.getMapper(FileDatailConverter.class);

    FtmFileDetailResponse toCopy(FileDetailResponse ftmFileDetailResponse);
}
