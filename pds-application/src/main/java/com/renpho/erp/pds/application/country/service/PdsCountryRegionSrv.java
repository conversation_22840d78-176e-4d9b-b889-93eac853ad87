package com.renpho.erp.pds.application.country.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import com.renpho.erp.pds.domain.common.LanguageRepository;
import com.renpho.erp.pds.domain.country.LanguageCountryRegion;
import com.renpho.erp.pds.domain.country.PdsCountryRegion;
import com.renpho.erp.pds.domain.country.PdsCountryRegionRepository;

import com.renpho.erp.pds.infrastructure.persistence.dto.CodeResponse;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsCountryRegionRequest;
import com.renpho.erp.pds.infrastructure.persistence.dto.PdsCountryRegionViewRequest;
import com.renpho.erp.pds.infrastructure.persistence.mapper.PdsCountryRegionMapper;

import com.renpho.erp.pds.infrastructure.persistence.po.PdsCountryRegionPo;
import com.renpho.erp.pds.infrastructure.utils.PagingUtils;
import com.renpho.karma.dto.Paging;
import jakarta.annotation.Resource;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 国家区域服务接口.
 *
 * <AUTHOR>
 * @since 2024.9.24
 */
@Service
public class PdsCountryRegionSrv {

    @Resource
    private PdsCountryRegionMapper pdsCountryRegionMapper;

    @Autowired
    private PdsCountryRegionRepository pdsCountryRegionRepository;

    @Autowired
    private LanguageRepository<LanguageCountryRegion> languageRepository;

    // 多语言表名
    private static final String L_NAME = "pds_country_region_language";

    /**
     * 添加/更新 多语言信息
     *
     * @param languages 语言信息
     * @param pk        主键
     * @param add       是否添加
     */
    public void saveLanguages(PdsCountryRegion languages, Integer pk, boolean add) {
        // 多语言
        List<LanguageCountryRegion> names = languages.getNames();
        if (names != null && !names.isEmpty()) {
            for (LanguageCountryRegion language : names) {
                language.setCountryRegionId(pk);
                if (add) {
                    languageRepository.insertLanguage(language, L_NAME);
                } else {
                    languageRepository.updateLanguage(language, L_NAME);
                }
            }
        }
    }

    /**
     * 查询多语言信息
     *
     * @param countryRegionId 区域主键ID
     * @return 多语言信息
     */
    public List<LanguageCountryRegion> getLanguages(Integer countryRegionId) {
        return languageRepository.selectByParentIdAndLanguage(countryRegionId, L_NAME, LanguageCountryRegion.class);
    }

    /**
     * 添加国家区域数据
     *
     * @param pdsCountryRegion 国家区域数据
     * @return 主键Id
     */
    @Transactional
    public int add(PdsCountryRegion pdsCountryRegion) {
        int pk = pdsCountryRegionRepository.savePdsCountryRegion(pdsCountryRegion);
        // 多语言
        saveLanguages(pdsCountryRegion, pk, true);

        return pk;
    }

    /**
     * 更新国家区域数据
     *
     * @param pdsCountryRegion 国家区域数据
     * @return 受影响条数
     */
    @Transactional
    public int update(PdsCountryRegion pdsCountryRegion) {
        // 多语言
        saveLanguages(pdsCountryRegion, pdsCountryRegion.getId().getId(), false);

        return pdsCountryRegionRepository.updatePdsCountryRegion(pdsCountryRegion);
    }

    /**
     * 禁用、启用
     *
     * @param pdsCountryRegion 国家区域数据
     * @return 受影响
     */
    public int changeStatus(PdsCountryRegion pdsCountryRegion) {
        return pdsCountryRegionRepository.updatePdsCountryRegion(pdsCountryRegion);
    }

    /**
     * 列表分页查询,单表
     *
     * @param pdsCountryRegionRequest 查询参数
     * @return 结果
     */
    public Paging<PdsCountryRegionPo> list(PdsCountryRegionRequest pdsCountryRegionRequest) {
        // 使用 PageHelper 进行分页
        int pageNum = pdsCountryRegionRequest.getPageIndex();
        int pageSize = pdsCountryRegionRequest.getPageSize();
        PageHelper.startPage(pageNum, pageSize);

        List<PdsCountryRegionPo> colors = pdsCountryRegionMapper.selectCountryRegions(pdsCountryRegionRequest.getKeyword(), pdsCountryRegionRequest.getCodes(), pdsCountryRegionRequest.getNames());
        return PagingUtils.toPaging(new PageInfo<>(colors));
    }


    /**
     * 根据ID获取国家区域信息
     *
     * @param id ID
     * @return 国家区域信息
     */
    public PdsCountryRegionPo getPdsCountryRegionDetail(Integer id) {
        if (id == null) {
            return null;
        }
        return this.pdsCountryRegionMapper.selectById(id);
    }

    /**
     * 根据ID集合获取国家区域信息
     * <p>
     * <br/>多语言信息 List<LanguageCountryRegion>  languages = PdsCountryRegionSrv.getLanguages(Integer countryRegionId)
     *
     * @param ids ID集合
     * @return 国家区域信息集合
     */
    public List<PdsCountryRegionPo> getPdsCountryRegionDetails(List<Integer> ids) {
        if (ids != null && !ids.isEmpty()) {
            return this.pdsCountryRegionMapper.selectBatchIds(ids);
        }
        return null;
    }

    /**
     * 查询国家区域信息
     *
     * @param codes code精确匹配
     * @return 查询结果列表
     */
    public List<PdsCountryRegionPo> searchCountryRegions(List<String> codes) {
        return pdsCountryRegionMapper.searchCountryRegions(codes);
    }

    /**
     * 主数据查询
     *
     * @param request 参数
     * @return 结果集
     */
    public Paging<PdsCountryRegionPo> viewSearch(PdsCountryRegionViewRequest request) {
        // 使用 PageHelper 进行分页
        int pageNum = request.getPageIndex();
        int pageSize = request.getPageSize();
        PageHelper.startPage(pageNum, pageSize);

        List<PdsCountryRegionPo> regions = pdsCountryRegionMapper.viewSearch(request);
        return PagingUtils.toPaging(new PageInfo<>(regions));
    }

    /**
     * 根据国家代码获取国家区域ID
     *
     * @param countryRegionCodeBasic 国家代码
     * @return 国家区域ID
     */
    public Integer getCountryRegionIdByCode(String countryRegionCodeBasic) {
        return pdsCountryRegionMapper.getIdByCode(countryRegionCodeBasic);
    }

    /**
     * 根据国家代码列表获取国家区域ID列表
     *
     * @param codes 国家代码列表
     * @return 国家区域ID列表
     */
    public Map<String, Integer> getCountryRegionMap(List<String> codes) {
        Map<String, Integer> map = new HashMap<>();
        List<CodeResponse> list = pdsCountryRegionMapper.getCountryRegionMap(codes);
        for (CodeResponse code : list) {
            map.put(code.getCode(), code.getId());
        }
        return map;
    }

    /**
     * 根据国家代码列表获取国家区域ID列表
     *
     * @param countryCodeList 国家代码列表
     * @return 国家区域ID列表
     */
    public List<Integer> getIdsByCountryCodes(List<String> countryCodeList) {
        if (countryCodeList == null || countryCodeList.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<PdsCountryRegionPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PdsCountryRegionPo::getCode, countryCodeList);

        List<PdsCountryRegionPo> countryRegions = pdsCountryRegionMapper.selectList(queryWrapper);
        if (countryRegions == null || countryRegions.isEmpty()) {
            return new ArrayList<>();
        }
        return countryRegions.stream().map(PdsCountryRegionPo::getId).toList();

    }

    /**
     * 根据国家区域ID列表获取国家区域信息列表
     *
     * @param countryRegionIds 国家区域ID列表
     * @return 国家区域信息列表
     */
    public List<PdsCountryRegionPo> getCountryRegionList(List<Integer> countryRegionIds) {
        if (countryRegionIds == null || countryRegionIds.isEmpty()) {
            return new ArrayList<>();
        }
        return pdsCountryRegionMapper.selectBatchIds(countryRegionIds);
    }
}
