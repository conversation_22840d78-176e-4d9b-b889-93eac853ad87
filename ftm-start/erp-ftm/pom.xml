<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.renpho.erp</groupId>
        <artifactId>ftm-start</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>erp-ftm</artifactId>
    <name>${project.artifactId}</name>
    <description>服务启动模块</description>

    <dependencies>
        <!-- ==================================== -->
        <!-- the 3rd part -->
        <!-- ==================================== -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>


        <dependency>
            <groupId>com.renpho.karma</groupId>
            <artifactId>karma-xfile</artifactId>
        </dependency>
        <!-- ==================================== -->
        <!-- 框架依赖 -->
        <!-- ==================================== -->
        <dependency>
            <groupId>com.renpho.karma</groupId>
            <artifactId>karma-boot</artifactId>
        </dependency>

        <!-- ==================================== -->
        <!-- 内部依赖 -->
        <!-- ==================================== -->
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>ftm-adapter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.renpho.karma</groupId>
            <artifactId>karma-xxljob</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>erp-ftm</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <!-- 指定该Main Class为全局的唯一入口 -->
                    <mainClass>com.renpho.erp.ftm.FileTransferManageSystemServeApplication</mainClass>
                    <includeSystemScope>true</includeSystemScope><!-- 把第三方jar包打进去 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal><!--可以把依赖的包都打包到生成的Jar包中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
