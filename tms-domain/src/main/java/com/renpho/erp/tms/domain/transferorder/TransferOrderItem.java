package com.renpho.erp.tms.domain.transferorder;

import com.renpho.erp.tms.domain.common.CreatedContainer;
import com.renpho.erp.tms.domain.common.UpdatedContainer;
import com.renpho.erp.tms.domain.inbound.BizPskuContainer;
import com.renpho.erp.tms.domain.inbound.InboundBusinessType;
import com.renpho.erp.tms.domain.operator.Operator;
import com.renpho.erp.tms.domain.owner.Owner;
import com.renpho.erp.tms.domain.owner.OwnerId;
import com.renpho.erp.tms.domain.product.Product;
import com.renpho.erp.tms.domain.product.ProductId;
import com.renpho.erp.tms.domain.saleschannel.SalesChannel;
import com.renpho.erp.tms.domain.saleschannel.SalesChannelId;
import com.renpho.erp.tms.domain.store.Store;
import com.renpho.erp.tms.domain.store.StoreId;
import lombok.Data;
import org.jmolecules.ddd.types.AggregateRoot;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 调拨单商品表
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Data
public class TransferOrderItem implements AggregateRoot<TransferOrderItem, TransferOrderItemId>, BizPskuContainer, CreatedContainer, UpdatedContainer, Serializable {

    @Serial
    private static final long serialVersionUID = 1717991588411360817L;

    private final TransferOrderItemId id;

    /**
     * 调拨单ID
     */
    private TransferOrderId tsId;

    /**
     * 调拨单号
     */
    private String tsNo;

    /**
     * 产品ID
     */
    private ProductId productId;

    /**
     * PSKU
     */
    private String psku;

    /**
     * FNSKU
     */
    private String fnsku;

    /**
     * 销售sku
     */
    private String sellerSku;

    /**
     * 产品图片
     */
    private String picture;

    /**
     * PSKU版本
     */
    private String productVersion;

    private Product product;

    /**
     * 销售渠道ID
     */
    private SalesChannelId salesChannelId;

    private SalesChannel salesChannel;

    /**
     * 店铺ID
     */
    private StoreId storeId;

    private Store store;

    /**
     * 货主ID
     */
    private OwnerId ownerId;

    private Owner owner;

    /**
     * 差异单号
     */
    private String discrepancyNo;

    /**
     * 出库单号
     */
    private String outboundNo;

    /**
     * 出库状态: 0待出库， 1 已出库
     */
    private Integer outboundStatus;

    /**
     * 出库时间
     */
    private LocalDateTime outboundTime;

    /**
     * 发货数量
     */
    private Integer qty;

    /**
     * 箱数
     */
    private BigDecimal boxQty;

    /**
     * 是否借货
     */
    private Boolean isBorrowed;

    /**
     * 是否换标
     */
    private Boolean isRelabel;

    /**
     * 是否换标完成
     */
    private Boolean isRelabelFinish;

    /**
     * 总毛重
     */
    private BigDecimal totalGrossWeight;

    /**
     * 总净重
     */
    private BigDecimal totalNetWeight;

    /**
     * 总体积
     */
    private BigDecimal totalVolume;

    /**
     * 换标完成时间
     */
    private LocalDateTime relabelFinishTime;

    /**
     * 销售金额
     */
    private BigDecimal saleAmount;

    /**
     * ASN标签文件ID, 仅 VC 类型有此文件, 展示时合并入箱唛文件
     */
    private List<String> asnLabelFileIds;

    /**
     * ASN条码号
     */
    private String asn;

    /**
     * 出借方PSKU
     */
    private String borrowedPsku;

    /**
     * 出借方FNSKU
     */
    private String borrowedFnsku;

    /**
     * 新产品标签文件ID
     */
    private String newProductLabelFileId;

    /**
     * 条码文件ID
     */
    private List<String> barcodeFileIds;

    /**
     * 换标完成文件
     */
    private List<Integer> relabelFinishFileIds;

    /**
     * 出借方店铺ID
     */
    private StoreId borrowedStoreId;

    private Store borrowedStore;

    /**
     * 出借方货主ID
     */
    private OwnerId borrowedOwnerId;

    private Owner borrowedOwner;

    /**
     * 发货开始时间
     */
    private LocalDateTime departureStartTime;

    /**
     * 发货结束时间
     */
    private LocalDateTime departureEndTime;

    /**
     * 发货要求
     */
    private String deliveryRequirement;

    /**
     * 是否打托, 0-否, 1-是
     */
    private Boolean isPalletized;

    /**
     * 签收数量
     */
    private Integer receivedQty;

    /**
     * 签收差异
     */
    private Integer receivedDiscrepancy;

    /**
     * 签收开始时间
     */
    private LocalDateTime receivedStartTime;

    /**
     * 签收结束时间
     */
    private LocalDateTime receivedEndTime;

    /**
     * 上架数量
     */
    private Integer putawayQty;

    /**
     * 上架差异
     */
    private Integer putawayDiscrepancy;

    /**
     * 上架开始时间
     */
    private LocalDateTime putawayStartTime;

    /**
     * 上架结束时间
     */
    private LocalDateTime putawayEndTime;

    /**
     * 尺寸单位
     */
    private String dimensionUnit;

    /**
     * 中英文品名
     */
    private String productNameCn;

    /**
     * 中英文品名
     */
    private String productNameEn;

    /**
     * 外箱尺寸-长
     */
    private BigDecimal boxLength;

    /**
     * 外箱尺寸-宽
     */
    private BigDecimal boxWidth;

    /**
     * 外箱尺寸-高
     */
    private BigDecimal boxHeight;

    /**
     * 重量单位
     */
    private String weightUnit;

    /**
     * 单品净重
     */
    private BigDecimal weight;

    /**
     * 单品毛重
     */
    private BigDecimal grossWeight;

    /**
     * 整箱毛重
     */
    private BigDecimal boxGrossWeight;

    /**
     * 装箱数量
     */
    private Integer quantityPerBox;

    /**
     * 创建信息
     */
    private Operator created;

    /**
     * 更新信息
     */
    private Operator updated;

    @Override
    public String getFnSku() {
        return fnsku;
    }

    @Override
    public String getBizNo() {
        return tsNo;
    }

    @Override
    public Integer getBizId() {
        return tsId.id();
    }

    @Override
    public InboundBusinessType getBizType() {
        return InboundBusinessType.TS;
    }

    /**
     * 检查是否是首次收货
     *
     * @return true:是首次收货
     */
    public boolean checkIsFirstReceived() {
        return receivedQty == null || receivedQty == 0;
    }

    /**
     * 计算签收数量和上架数量的差异值，如果签收数量有差异值，则返回true
     *
     * @param actualReceivedQty 实际签收数量
     * @param actualShelvedQty  实际上架数量
     */
    public boolean calculateTheDifferenceValue(Integer actualReceivedQty, Integer actualShelvedQty) {
        this.receivedDiscrepancy = actualReceivedQty - this.qty;
        this.putawayDiscrepancy = actualShelvedQty - this.qty;

        return this.putawayDiscrepancy != 0;
    }

    public void updateByTransferOrder(TransferOrder ts) {
        setTsId(ts.getId());
        setTsNo(ts.getTsNo());
        setSalesChannelId(ts.getSalesChannelId());
        setSalesChannel(ts.getSalesChannel());
        setStoreId(ts.getStoreId());
        setStore(ts.getStore());
        setOwnerId(ts.getOwnerId());
        setOwner(ts.getOwner());
        setCreated(ts.getCreated());
        setUpdated(ts.getUpdated());
    }

    public void sumBoxQty() {
        this.boxQty = BigDecimal.valueOf(this.qty).divide(BigDecimal.valueOf(this.quantityPerBox), 0, RoundingMode.UP);
    }

    public void sumVolume() {
        this.totalVolume = this.boxQty.multiply(this.product.getActiveBoxSpec().calculateVolume().orElse(BigDecimal.ZERO));
    }

    public void sumGrossWeight() {
        this.totalGrossWeight = this.boxQty.multiply(this.product.getActiveBoxSpec().getGrossWeightPerBoxMetric());
    }

    public void sumNetWeight() {
        this.totalNetWeight = this.boxQty.multiply(this.product.getWeightMetric());
    }

    public void sumBoxGrossWeight() {
        this.boxGrossWeight = this.boxQty.multiply(this.product.getActiveBoxSpec().getGrossWeightPerBoxMetric());
    }

    public void fillProduct(Product product) {
        if (product == null) {
            return;
        }
        product.calculateBoxVolume();
        product.calculateVolume();
        product.calculatePackageVolume();
        this.setProduct(product);
        this.sumBoxQty();
        this.sumVolume();
        this.sumGrossWeight();
        this.sumNetWeight();
        this.sumBoxGrossWeight();
    }
}
