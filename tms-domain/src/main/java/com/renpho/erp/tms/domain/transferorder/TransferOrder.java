package com.renpho.erp.tms.domain.transferorder;

import com.renpho.erp.generator.util.GeneratorCodeUtil;
import com.renpho.erp.tms.domain.common.CreatedContainer;
import com.renpho.erp.tms.domain.common.ReceiptGenerationType;
import com.renpho.erp.tms.domain.common.UpdatedContainer;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.inbound.BizPskuWarehouseContainer;
import com.renpho.erp.tms.domain.inbound.InboundBusinessType;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.logisticsmode.LogisticsMode;
import com.renpho.erp.tms.domain.logisticsmode.LogisticsModeId;
import com.renpho.erp.tms.domain.operator.Operator;
import com.renpho.erp.tms.domain.operator.OperatorId;
import com.renpho.erp.tms.domain.orderfile.OrderFile;
import com.renpho.erp.tms.domain.owner.Owner;
import com.renpho.erp.tms.domain.owner.OwnerId;
import com.renpho.erp.tms.domain.processinstance.ProcessInstanceId;
import com.renpho.erp.tms.domain.saleschannel.SalesChannel;
import com.renpho.erp.tms.domain.saleschannel.SalesChannelId;
import com.renpho.erp.tms.domain.site.CountryRegion;
import com.renpho.erp.tms.domain.store.Store;
import com.renpho.erp.tms.domain.store.StoreId;
import com.renpho.erp.tms.domain.transportorder.ApprovalStatus;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.erp.tms.domain.warehouse.Warehouse;
import com.renpho.erp.tms.domain.warehouse.WarehouseId;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jmolecules.ddd.types.AggregateRoot;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

/**
 * 调拨单主表
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Data
public class TransferOrder implements AggregateRoot<TransferOrder, TransferOrderId>, BizPskuWarehouseContainer, CreatedContainer, UpdatedContainer, Serializable {

    @Serial
    private static final long serialVersionUID = 7840268419798885335L;

    private final TransferOrderId id;

    /**
     * 调拨单单号
     */
    private String tsNo;

    /**
     * OMS的单号
     */
    private String orderNo;

    /**
     * OMS的订单参考号
     */
    private String refNo;

    /**
     * 调拨单状态, 字典: transfer_order_status
     */
    private TransferOrderStatus status;

    /**
     * 调拨单类型, 字典: transfer_order_type
     */
    private TransferOrderType type;

    /**
     * 业务类型, 字典: transfer_order_biz_type
     */
    private TransferOrderBizType tsBizType;

    /**
     * 流程实例id
     */
    private ProcessInstanceId instanceId;

    /**
     * 流程实例业务编码
     */
    private String instanceBizCode;

    /**
     * 审批结果
     */
    private ApprovalStatus approvalResult;
    /**
     * 差异单号
     */
    private String discrepancyNo;
    /**
     * 差异单状态
     */
    private String discrepancyStatus;

    /**
     * 数据来源, 判断是TMS创建还是上游传入
     */
    private TransferOrderDataSource dataSource;

    /**
     * 销售渠道ID
     */
    private SalesChannelId salesChannelId;

    private SalesChannel salesChannel;

    /**
     * 店铺ID
     */
    private StoreId storeId;

    private Store store;

    /**
     * 货主ID
     */
    private OwnerId ownerId;

    private Owner owner;

    /**
     * 发货仓
     */
    private WarehouseId shippingWarehouseId;

    /**
     * 发货仓Code
     */
    private String shippingWarehouseCode;

    private Warehouse shippingWarehouse;

    /**
     * 目的国/地区
     */
    private String destCountryCode;

    private CountryRegion destCountry;

    /**
     * 目的仓库ID
     */
    private WarehouseId destWarehouseId;

    /**
     * 目的仓Code
     */
    private String destWarehouseCode;

    private Warehouse destWarehouse;

    /**
     * 目的地
     */
    private String destAddress;

    /**
     * 贸易条款, 字典: trade_terms
     */
    private String tradeTerms;

    /**
     * 付款条款, 字典: payment_terms
     */
    private String paymentTerms;

    /**
     * 是否打托
     */
    private Boolean isPalletized;

    /**
     * 预估发货时间
     */
    private LocalDateTime estimatedDepartureTime;

    /**
     * 预估交货时间
     */
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 预估到达时间
     */
    private LocalDateTime estimatedArrivalTime;

    /**
     * 实际发货时间
     */
    private LocalDateTime actualDepartureTime;

    /**
     * 实际交货时间
     */
    private LocalDateTime actualDeliveryTime;

    /**
     * 期望上架时间
     */
    private LocalDateTime expectedPutawayTime;

    /**
     * 总数量
     */
    private Integer qty;

    /**
     * 总箱数
     */
    private BigDecimal boxQty;

    /**
     * 总毛重
     */
    private BigDecimal grossWeight;

    /**
     * 总净重
     */
    private BigDecimal netWeight;

    /**
     * 总体积
     */
    private BigDecimal volume;

    /**
     * 提单类型, 字典: bill_of_lading_type
     */
    private String billOfLadingType;

    /**
     * 物流方式ID
     */
    private LogisticsModeId logisticsModeId;

    /**
     * 物流方式
     */
    private LogisticsMode logisticsMode;

    /**
     * 运输类型, 字典：TRANSPORT_TYPE
     */
    private String transportType;

    /**
     * 运单号
     */
    private String trackingNumber;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 发货Id
     */
    private String shipmentId;

    /**
     * 参考ID
     */
    private String referenceId;

    /**
     * 允许发货, 0-不允许, 1-允许
     */
    private Boolean isAllowedShipping;

    /**
     * 关闭时间
     */
    private LocalDateTime closedTime;

    /**
     * 运营人员ID
     */
    private OperatorId salesStaffId;

    private Operator salesStaff;

    /**
     * 计划人员ID
     */
    private OperatorId planerStaffId;

    private Operator planerStaff;

    /**
     * 船务人员ID
     */
    private OperatorId shippingStaffId;

    private Operator shippingStaff;

    /**
     * 创建信息
     */
    private Operator created;

    /**
     * 更新信息
     */
    private Operator updated;

    /**
     * 调拨单商品列表
     */
    private List<TransferOrderItem> items;

    /**
     * 调拨单客户信息
     */
    private TransferOrderCustomer customer;

    /**
     * 同步API状态
     */
    private SyncApiStatus syncApiStatus;

    /**
     * 状态历史
     */
    private List<TransferOrderStatusHistory> statusHistoryList;

    /**
     * 批注列表
     */
    private List<TransferOrderComment> comments;

    /**
     * 箱唛文件列表
     */
    private List<Integer> cartonLabelFileIds;

    /**
     * 文件列表
     */
    private List<OrderFile> files;

    @Override
    public String getBizNo() {
        return tsNo;
    }

    @Override
    public Integer getBizId() {
        return id.id();
    }

    @Override
    public TransferOrderId getTsId() {
        return this.id;
    }

    @Override
    public InboundBusinessType getBizType() {
        return InboundBusinessType.TS;
    }

    public void createTsNo() {
        this.tsNo = GeneratorCodeUtil.generateIncr(ReceiptGenerationType.TS.name());
    }

    public Integer sourceId() {
        return this.id.id();
    }

    /**
     * 根据仓库服务商类型判断仓库API类型
     *
     * <AUTHOR>
     * @since 2025/7/16
     */
    public WarehouseProviderType findWarehouseProviderType() {
        return getWarehouseType();
    }

    @Override
    public String getPsku() {
        return "";
    }

    @Override
    public String getFnSku() {
        return "";
    }

    private Integer sum(Function<TransferOrderItem, Integer> function) {
        return CollectionUtils.emptyIfNull(this.items).stream().mapToInt(function::apply).sum();
    }

    public Integer getTotalReceivedQty() {
        return sum(TransferOrderItem::getReceivedQty);
    }

    public Integer getTotalReceivedDiscrepancy() {
        return sum(TransferOrderItem::getReceivedDiscrepancy);
    }

    public Integer getTotalPutawayQty() {
        return sum(TransferOrderItem::getPutawayQty);
    }

    public Integer getTotalPutawayDiscrepancy() {
        return sum(TransferOrderItem::getPutawayDiscrepancy);
    }

    /**
     * 是否有差异
     * @return true/false
     */
    public boolean haveDiscrepancy(){
        return getTotalPutawayDiscrepancy() > 0;
    }

    public void fillType() {
        this.type = TransferOrderType.fromTransferOrder(this);
    }

    public String getOutboundBusinessType() {
        if (tsBizType != null) {
            if (tsBizType == TransferOrderBizType.B2C_REPLENISHMENT && "AMZ".equalsIgnoreCase(salesChannel.getChannelCode())) {
                return "FBA";
            }
            if (tsBizType == TransferOrderBizType.VC_DO) {
                return "VC";
            }

        }
        return "Other";
    }

    /**
     * 断言状态
     *
     * @param statuses 状态
     */
    public void assertStatusIn(TransferOrderStatus... statuses) {
        if (!Arrays.asList(statuses).contains(this.status)) {
            //当前状态不允许操作
            throw new BusinessException("error.ts.status-not-allow-operation");

        }
    }

    /**
     * 根据审批状态返回单据状态
     *
     * <AUTHOR>
     * @since 2025/8/29 11:42
     */
    public TransferOrderStatus determineFinalStatus(TransferOrder order, ApprovalStatus approvalStatus) {
        return switch (approvalStatus) {
            case APPROVED -> (order.getTsBizType() == TransferOrderBizType.B2B_CUSTOMER
                              || order.getTsBizType() == TransferOrderBizType.VC_DO)
                    ? TransferOrderStatus.WAIT_CREATE_SHIPMENT
                    : order.getStatus();
            case REJECTED -> TransferOrderStatus.REJECTED;
            case CANCEL, VOID -> TransferOrderStatus.DRAFT;
            default -> order.getStatus();
        };
    }

    public void fillOutboundNo(String outboundNo) {
        for (TransferOrderItem item : CollectionUtils.emptyIfNull(this.items)) {
            item.setOutboundNo(outboundNo);
        }
    }

    public void sumQty() {
        this.qty = CollectionUtils.emptyIfNull(this.items).stream().mapToInt(TransferOrderItem::getQty).sum();
    }

    public void sumBoxQty() {
        this.boxQty = CollectionUtils.emptyIfNull(this.items).stream().map(TransferOrderItem::getBoxQty).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public void sumVolume() {
        this.volume = CollectionUtils.emptyIfNull(this.items).stream().map(TransferOrderItem::getTotalVolume).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public void sumGrossWeight() {
        this.grossWeight = CollectionUtils.emptyIfNull(this.items).stream().map(TransferOrderItem::getTotalGrossWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public void sumNetWeight() {
        this.netWeight = CollectionUtils.emptyIfNull(this.items).stream().map(TransferOrderItem::getTotalNetWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public void sum() {
        this.sumQty();
        this.sumBoxQty();
        this.sumVolume();
        this.sumGrossWeight();
        this.sumNetWeight();
    }

    public void markRelabelFinish(String outboundNo, LocalDateTime finishTime) {
        for (TransferOrderItem item : CollectionUtils.emptyIfNull(this.items)) {
            if (StringUtils.equals(outboundNo, item.getOutboundNo())) {
                item.setRelabelFinishTime(finishTime);
                item.setIsRelabelFinish(true);
            }
        }
    }
}
