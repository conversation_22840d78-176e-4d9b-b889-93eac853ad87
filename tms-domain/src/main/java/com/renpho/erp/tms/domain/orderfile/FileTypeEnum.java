package com.renpho.erp.tms.domain.orderfile;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

/**
 * 文件类型
 * TMS_TO_FILE_TYPE
 *
 * <AUTHOR>
 * @date 2025/6/16
 */
@Getter
@AllArgsConstructor
public enum FileTypeEnum {

    /**
     * 货代报价单
     */
    FREIGHT_QUOTE("TO", "FREIGHT_QUOTE", "Freight Quote", "货代报价单"),

    /**
     * 提单
     */
    BILL_OF_LADING("TO", "BILL_OF_LADING", "Bill Of Lading", "提单"),

    /**
     * 保单
     */
    INSURANCE_POLICY("TO", "INSURANCE_POLICY", "Insurance Policy", "保单"),

    /**
     * POD
     */
    POD("TO", "POD", "POD", "POD"),

    /**
     * 报关单
     */
    CUSTOMS_DECLARATION("TO", "CUSTOMS_DECLARATION", "Customs Declaration", "报关单"),

    /**
     * 箱单
     */
    PACKING_LIST("TO", "PACKING_LIST", "Packing List", "箱单"),

    /**
     * 发票
     */
    INVOICE("TO", "INVOICE", "Invoice", "发票"),

    /**
     * 海关税单
     */
    CUSTOMS_TAX_RECEIPT("TO", "CUSTOMS_TAX_RECEIPT", "Customs Tax Receipt", "海关税单"),

    /**
     * 产地证
     */
    CERTIFICATE_OF_ORIGIN("TO", "CERTIFICATE_OF_ORIGIN", "Certificate Of Origin", "产地证"),

    /**
     * 其他文件
     */
    OTHER("TO", "OTHER", "Other", "其他文件"),

    /**
     * 箱唛文件
     */
    CARTON_LABEL("TS", "CARTON_LABEL", "Carton Label", "箱唛文件"),

    /**
     * 发货证明，交货单/提单
     */
    PROOF_OF_DELIVERY("TS", "PROOF_OF_DELIVERY", "Proof of Delivery", "提单/交接单"),

    /**
     * 换标完成证明
     */
    PROOF_OF_RELABEL_COMPLETED("TS", "PROOF_OF_RELABEL_COMPLETED", "Proof of Relabel Completed", "换标完成证明"),

    /**
     * 签收证明
     */
    PROOF_OF_RECEIPT ("TS", "PROOF_OF_RECEIPT", "Proof of Receipt", "签收证明"),
    ;

    private final String businessType;

    private final String code;

    private final String descEn;

    private final String descCn;

    public String desc() {
        return Locale.US.toLanguageTag().equals(LocaleContextHolder.getLocale().toLanguageTag()) ? this.descEn : this.descCn;
    }

    /**
     * 根据业务类型获取
     *
     * @param businessType 业务类型
     * @return 文件类型
     */
    public static List<FileTypeEnum> getByType(BusinessTypeEnum businessType) {
        return Arrays.stream(FileTypeEnum.values())
                .filter(e -> businessType.getCode().equals(e.getBusinessType())).collect(Collectors.toList());
    }

    public static FileTypeEnum getFileTypeByCode(String code) {
        for (FileTypeEnum fileTypeEnum : FileTypeEnum.values()) {
            if (fileTypeEnum.getCode().equals(code)) {
                return fileTypeEnum;
            }
        }
        return null;
    }

    /**
     * TO头程单文件类型数量
     * @return 所有文件类型数量
     */
    public static Integer toTypeSize() {
        return 10;
    }

}
