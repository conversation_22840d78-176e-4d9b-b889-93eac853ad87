package com.renpho.erp.tms.adapter.web.controller.transferorder.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.github.houbb.sensitive.annotation.Sensitive;
import com.renpho.erp.data.sensitive.SensitiveDynamicCondition;
import com.renpho.erp.data.sensitive.SensitiveDynamicStrategy;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
public class TransferOrderExportVO implements VO {

    @ExcelIgnore
    private Integer id;

    /**
     * 调拨单单号
     */
    @ExcelProperty(value = "export.ts.ts-no", index = 0)
    private String tsNo;

    /**
     * 调拨单状态, 字典: transfer_order_status
     */
    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "transfer_order_status", ref = "statusName")
    private Integer status;

    @ExcelProperty(value = "export.ts.status", index = 1)
    private String statusName;

    /**
     * 业务类型, 字典: transfer_order_biz_type
     */
    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "transfer_order_biz_type", ref = "tsBizTypeName")
    private String tsBizType;

    @ExcelProperty(value = "export.ts.biz-type", index = 2)
    private String tsBizTypeName;


    /**
     * 调拨单类型, 字典: transfer_order_type
     */
    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "transfer_order_type", ref = "typeName")
    private String type;

    @ExcelProperty(value = "export.ts.type", index = 3)
    private String typeName;

    /**
     * PSKU
     */
    @ExcelProperty(value = "export.ts.psku", index = 4)
    private String psku;

    /**
     * FNSKU
     */
    @ExcelProperty(value = "export.ts.fnsku", index = 5)
    private String fnsku;

    /**
     * 中英文品名
     */
    @ExcelProperty(value = "export.ts.psku-name", index = 6)
    private String productNameEn;

    /**
     * 发货数量
     */
    @ExcelProperty(value = "export.ts.total-quantity", index = 7)
    private Integer qty;

    /**
     * 箱数
     */
    @ExcelProperty(value = "export.ts.total-cartons", index = 8)
    private BigDecimal boxQty;

    /**
     * 总毛重
     */
    @ExcelProperty(value = "export.ts.total-gross-weight", index = 9)
    private BigDecimal totalGrossWeight;

    /**
     * 总体积
     */
    @ExcelProperty(value = "export.ts.total-volume", index = 10)
    private BigDecimal totalVolume;

    /**
     * 发货仓
     */
    @ExcelProperty(value = "export.ts.shipping-warehouse", index = 11)
    private String shippingWarehouse;

    /**
     * 目的仓
     */
    @ExcelProperty(value = "export.ts.destination-warehouse", index = 12)
    private String destWarehouseName;

    /**
     * 目的国/地区
     */
    @ExcelProperty(value = "export.ts.destination-country", index = 13)
    private String destCountryCode;

    /**
     * 目的仓Code
     */
    @ExcelProperty(value = "export.ts.destination-warehouse-code", index = 14)
    private String destWarehouseCode;

    /**
     * 目的地
     */
    @ExcelProperty(value = "export.ts.destination", index = 15)
    private String destAddress;

    /**
     * 贸易条款, 字典: trade_terms
     */
    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "trade_terms", ref = "tradeTermsName")
    private String tradeTerms;

    /**
     * 贸易条款, 字典: trade_terms
     */
    @ExcelProperty(value = "export.ts.trade-terms", index = 16)
    private String tradeTermsName;

    /**
     * 销售渠道名称
     */
    @ExcelProperty(value = "export.ts.sales-channel", index = 17)
    private String salesChannelName;

    /**
     * 货主名称
     */
    @ExcelProperty(value = "export.ts.owner", index = 18)
    private String ownerName;

    /**
     * 店铺名称
     */
    @ExcelProperty(value = "export.ts.store", index = 19)
    private String storeName;
    /**
     * 是否借货
     */
    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "is_relabel", ref = "isBorrowedName")
    private Boolean isBorrowed;

    @ExcelProperty(value = "export.ts.is-borrowed", index = 20)
    private String isBorrowedName;

    /**
     * 是否换标
     */
    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "is_relabel", ref = "isRelabelName")
    private Boolean isRelabel;

    /**
     * 是否换标
     */
    @ExcelProperty(value = "export.ts.is-relabel", index = 21)
    private String isRelabelName;

    /**
     * 出借方PSKU
     */
    @ExcelProperty(value = "export.ts.borrowed-psku", index = 22)
    private String borrowedPsku;

    /**
     * 出借方FNSKU
     */
    @ExcelProperty(value = "export.ts.borrowed-fnsku", index = 23)
    private String borrowedFnsku;

    /**
     * 出借方店铺名称
     */
    @ExcelProperty(value = "export.ts.borrowed-store", index = 24)
    private String borrowedStoreName;

    /**
     * 出借方货主名称
     */
    @ExcelProperty(value = "export.ts.borrowed-owner", index = 25)
    private String borrowedOwnerName;

    /**
     * 发货Id
     */
    @ExcelProperty(value = "export.ts.shipmentId", index = 26)
    private String shipmentId;

    /**
     * 入库单对应referenceId
     */
    @ExcelProperty(value = "export.ts.referenceId", index = 27)
    private String referenceId;

    /**
     * 预估发货时间
     */
    @ExcelProperty(value = "export.ts.estimated-departure-time", index = 28)
    private LocalDateTime estimatedDepartureTime;

    /**
     * 预估交货时间
     */
    @ExcelProperty(value = "export.estimated-delivery-time", index = 29)
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 期望上架时间
     */
    @ExcelProperty(value = "export.ts.expected-putaway-time", index = 30)
    private LocalDateTime expectedPutawayTime;

    /**
     * 出库单号
     */
    @ExcelProperty(value = "export.ts.outbound-no", index = 31)
    private String outboundNo;

    /**
     * 发货开始时间
     */
    @ExcelProperty(value = "export.ts.departure-start-time", index = 32)
    private LocalDateTime departureStartTime;

    /**
     * OMS的单号
     */
    @ExcelProperty(value = "export.ts.order-no", index = 33)
    private String orderNo;

    /**
     * OMS的订单参考号
     */
    @ExcelProperty(value = "export.ts.ref-no", index = 34)
    private String refNo;
}

