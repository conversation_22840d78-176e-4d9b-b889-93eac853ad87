package com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.converter;

import com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.AddItemCmd;
import com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.TransferOrderMarkRelabelFinishCmd;
import com.renpho.erp.tms.domain.transferorder.TransferOrder;
import com.renpho.erp.tms.domain.transferorder.TransferOrderItem;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderConverter;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderItemConverter;
import com.renpho.erp.tms.infrastructure.remote.owner.converter.OwnerConverter;
import com.renpho.erp.tms.infrastructure.remote.product.converter.ProductConverter;
import com.renpho.erp.tms.infrastructure.remote.saleschannel.converter.SalesChannelConverter;
import com.renpho.erp.tms.infrastructure.remote.store.converter.StoreConverter;
import com.renpho.erp.tms.infrastructure.remote.warehouse.converter.WarehouseConverter;
import org.mapstruct.*;

import java.util.Collection;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/8/23
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {TransferOrderItemConverter.class, SalesChannelConverter.class, StoreConverter.class, OwnerConverter.class, TransferOrderConverter.class, WarehouseConverter.class, ProductConverter.class})
public interface TransferOrderItemCmdConverter {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tsId", ignore = true)
    @Mapping(target = "borrowedStoreId", source = "borrowedStore.id")
    @Mapping(target = "borrowedStore", source = "borrowedStore")
    @Mapping(target = "borrowedOwnerId", source = "borrowedOwner.id")
    @Mapping(target = "borrowedOwner.id", source = "borrowedOwner")
    TransferOrderItem toDomain(AddItemCmd cmd);

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<TransferOrderItem> toDomains(Collection<AddItemCmd> cmd);

    @Mapping(target = "tsId", source = "tsId")
    @Mapping(target = "relabelFinishFileIds", source = "fileIds")
    @Mapping(target = "relabelFinishTime", source = "finishTime")
    TransferOrderItem toDomain(TransferOrderMarkRelabelFinishCmd cmd);

}
