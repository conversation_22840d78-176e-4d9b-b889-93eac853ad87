package com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.converter;

import com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.*;
import com.renpho.erp.tms.domain.transferorder.TransferOrder;
import com.renpho.erp.tms.domain.transferorder.TransferOrderId;
import com.renpho.erp.tms.domain.transferorder.TransferOrderQuery;
import com.renpho.erp.tms.domain.transferorder.TransferOrderStatus;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderConverter;
import com.renpho.erp.tms.infrastructure.remote.owner.converter.OwnerConverter;
import com.renpho.erp.tms.infrastructure.remote.saleschannel.converter.SalesChannelConverter;
import com.renpho.erp.tms.infrastructure.remote.store.converter.StoreConverter;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import com.renpho.erp.tms.infrastructure.remote.warehouse.converter.WarehouseConverter;
import org.mapstruct.*;

import java.util.Collection;
import java.util.Optional;
import java.util.Set;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/8/23
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {TransferOrderItemCmdConverter.class, TransferOrderCustomerCmdConverter.class, TransferOrderConverter.class, SalesChannelConverter.class, StoreConverter.class, OwnerConverter.class, TransferOrderConverter.class, WarehouseConverter.class, OperatorConverter.class},
        imports = {Optional.class, TransferOrderStatus.class}
)
public interface TransferOrderCmdConverter {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tsNo", ignore = true)
    @Mapping(target = "items", source = "items")
    @Mapping(target = "created.operatorId.id", source = "planerStaffId")
    @Mapping(target = "updated.operatorId.id", source = "planerStaffId")
    @Mapping(target = "planerStaffId.id", source = "planerStaffId")
    @Mapping(target = "planerStaff.operatorId.id", source = "planerStaffId")
    @Mapping(target = "salesChannelId.id", source = "store.salesChannelId")
    @Mapping(target = "salesStaffId.id", source = "salesStaffId")
    @Mapping(target = "storeId", source = "store.id")
    @Mapping(target = "store", source = "store")
    @Mapping(target = "ownerId", source = "store.activeOwner.id")
    @Mapping(target = "owner", source = "store.activeOwner")
    @Mapping(target = "shippingWarehouseId", source = "shippingWarehouseId")
    @Mapping(target = "shippingWarehouse.id", source = "shippingWarehouseId")
    @Mapping(target = "shippingWarehouse.code", source = "shippingWarehouseCode")
    @Mapping(target = "destWarehouseId", source = "destWarehouseId")
    @Mapping(target = "destWarehouse.id", source = "destWarehouseId")
    @Mapping(target = "destWarehouse.code", source = "destWarehouseCode")
    @Mapping(target = "status", expression = "java(TransferOrderStatus.DRAFT)")
    @BeanMapping(qualifiedByName = {"fillTsNo", "fillType"})
    TransferOrder toDomain(AddCmd cmd);

    @AfterMapping
    @Named("fillTsNo")
    default void fillTsNo(@MappingTarget TransferOrder domain) {
        domain.createTsNo();
    }

    @AfterMapping
    @Named("fillType")
    default void fillType(@MappingTarget TransferOrder domain) {
        domain.fillType();
    }

    @InheritConfiguration(name = "toDomain")
    @Mapping(target = "created", ignore = true)
    @Mapping(target = "updated.operatorId", source = "updateBy")
    @BeanMapping(qualifiedByName = {"fillType"})
    TransferOrder toDomain(UpdateCmd cmd);

    TransferOrderQuery toDetailDomain(TransferOrderDetailCmd cmd);

    /*@Mapping(target = "id", source = "id")
    TransferOrderId toId(Integer id);*/

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    Set<TransferOrderId> toIds(Collection<Integer> id);

    @Mapping(target = "id.id", source = "tsId")
    @Mapping(target = "cartonLabelFileIds", source = "fileIds")
    TransferOrder toDomain(TransferOrderUploadShipmentCmd cmd);

    default Set<TransferOrderId> toIds(IdsCmd cmd) {
        return Optional.ofNullable(cmd).map(IdsCmd::getIds).map(this::toIds).orElse(Set.of());
    }

}

