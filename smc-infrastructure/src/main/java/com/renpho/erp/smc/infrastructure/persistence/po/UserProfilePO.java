package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.StatusPO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户信息 PO.
 *
 * <AUTHOR>
 */
@Data
@TableName("smc_user_profile")
public class UserProfilePO extends StatusPO<Integer, Integer, Integer, LocalDateTime, Boolean> {

	/**
	 * 用户名称
	 */
	private String name;

}
