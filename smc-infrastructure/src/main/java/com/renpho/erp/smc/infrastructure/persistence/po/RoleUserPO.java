package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;
import lombok.Data;

/**
 * 角色 PO.
 *
 * <AUTHOR>
 */
@Data
@TableName("oum_role_user")
public class RoleUserPO extends DefaultPO {

	// region 本表字段

	/**
	 * 角色ID
	 */
	private Integer roleId;

	/**
	 * 用户ID
	 */
	private Integer userId;

	// endregion

	// region 非本表字段

	/**
	 * 用户工号
	 */
	@TableField(exist = false)
	private String userCode;

	/**
	 * 用户姓名
	 */
	@TableField(exist = false)
	private String userName;

	/**
	 * 邮箱
	 */
	@TableField(exist = false)
	private String email;

	/**
	 * 岗位
	 */
	@TableField(exist = false)
	private String positionName;

	/**
	 * 部门
	 */
	@TableField(exist = false)
	private String departmentName;

	/**
	 * 用户状态，1 在职，2 离职
	 */
	@TableField(exist = false)
	private Integer userStatus;

	/**
	 * 账号状态，0 禁用、1 启动、2 冻结
	 */
	@TableField(exist = false)
	private Integer accountStatus;

	/**
	 * 语言
	 */
	@TableField(exist = false)
	private String language;

	// endregion

}
