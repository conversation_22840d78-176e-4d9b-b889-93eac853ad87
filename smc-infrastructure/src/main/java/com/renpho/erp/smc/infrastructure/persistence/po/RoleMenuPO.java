package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 角色菜单 PO.
 *
 * <AUTHOR>
 */
@Data
@TableName("oum_role_menu")
public class RoleMenuPO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> {

	/**
	 * 角色ID
	 */
	private Integer roleId;

	/**
	 * 菜单ID
	 */
	private Integer menuId;

}
