package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.CreationPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 钉钉日程
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_ding_ding_schedule")
public class DingDingSchedulePO extends CreationPO<Integer, Integer, LocalDateTime> implements Serializable {

	/**
	 * 开始时间
	 */
	@TableField(value = "start_time")
	private LocalDateTime startTime;

	/**
	 * 结束时间
	 */
	@TableField(value = "end_time")
	private LocalDateTime endTime;

	/**
	 * 内容
	 */
	@TableField(value = "content")
	private String content;

	/**
	 * 地点
	 */
	@TableField(value = "venue")
	private String venue;

	/**
	 * 日期
	 */
	@TableField(value = "`day`")
	private LocalDate day;

	/**
	 * erp系统id
	 */
	@TableField(value = "user_id")
	private Integer userId;

	/**
	 * 钉钉用户id
	 */
	@TableField(value = "ding_ding_user_id")
	private String dingDingUserId;

	@TableField(value = "ding_ding_union_id")
	private String dingDingUnionId;

	/**
	 * 状态
	 */
	@TableField(value = "status")
	private String status;

	@TableField(value = "is_all_day")
	private String isAllDay;

	@TableField(value = "req")
	private String req;

	@TableField(value = "resp")
	private String resp;

	@TableLogic
	@TableField("is_deleted")
	private Integer deleted;

	@Serial
	private static final long serialVersionUID = 1L;

}