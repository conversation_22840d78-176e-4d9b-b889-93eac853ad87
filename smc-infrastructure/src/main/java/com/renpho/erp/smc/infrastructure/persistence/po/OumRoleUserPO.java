package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;

import lombok.Data;

/**
 * @description: 角色用户表
 * @author: doctor
 * @date: 2024/09/18
 * @version: 1.0.0
 */
@Data
@TableName("oum_role_user")
public class OumRoleUserPO extends DefaultPO {

	/** 角色ID **/
	private Integer roleId;

	/** 用户ID **/
	private Integer userId;

}
