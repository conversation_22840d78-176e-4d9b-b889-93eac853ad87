package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * @description: 钉钉用户表
 * @author: doctor
 * @date: 2024/09/13
 * @version: 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("oum_dingtalk_user")
public class OumDingTalkUser extends DefaultPO {

	@Serial
	private static final long serialVersionUID = 1L;

	/** 部门id **/
	private Long deptId;

	/** 员工姓名 **/
	private String name;

	/** 第三方平台头像 **/
	private String avatarUrl;

	/** 邮箱 **/
	private String email;

	/** 员工在当前开发者企业账号范围内的唯一标识 **/
	private String unionid;

	/** 系统用户ID **/
	private Integer sysUserId;

	/** 员工的userId **/
	private String userid;

}
