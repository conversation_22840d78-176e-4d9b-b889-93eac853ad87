package com.renpho.erp.smc.infrastructure.persistence.po;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.erp.smc.domain.systemsetting.MultiLanguage;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("oum_department")
public class DepartmentPO extends DefaultPO {

	/**
	 * 上级部门
	 */
	private Integer parentId;

	/**
	 * 部门编码
	 */
	private String code;

	/**
	 * 部门领导，取值user表的id
	 */
	private Integer managerId;

	/**
	 * 部门层级标签
	 */
	private Integer levelLabel;

	/**
	 * 排序，默认 0
	 */
	private Integer sort;

	@TableField(exist = false)
	private Integer pageNum;

	@TableField(exist = false)
	private Integer pageSize;

	@TableField(exist = false)
	private List<MultiLanguage> names;

	@TableField(exist = false)
	private String parentName;

	@TableField(exist = false)
	private String parentCode;

	@TableField(exist = false)
	private String managerName;

	@TableField(exist = false)
	private String name;

	@TableField(exist = false)
	private List<DepartmentPO> children;

	@TableField(exist = false)
	private String userJson;

}
