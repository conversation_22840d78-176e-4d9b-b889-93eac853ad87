package com.renpho.erp.smc.infrastructure.persistence.po;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("oum_role_language")
public class RoleLanguagePO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> {

	/**
	 * 角色ID
	 */
	private Integer roleId;

	/**
	 * 语言
	 */
	private String language;

	/**
	 * 名称
	 */
	private String name;

}
