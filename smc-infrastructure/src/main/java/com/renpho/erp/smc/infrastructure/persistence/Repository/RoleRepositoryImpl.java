package com.renpho.erp.smc.infrastructure.persistence.Repository;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Sets;
import com.renpho.erp.smc.domain.authoritymanagement.role.Role;
import com.renpho.erp.smc.domain.authoritymanagement.role.RoleRepository;
import com.renpho.erp.smc.domain.authoritymanagement.role.UserDataPermission;
import com.renpho.erp.smc.domain.authoritymanagement.role.UserFieldPermission;
import com.renpho.erp.smc.domain.systemsetting.MultiLanguage;
import com.renpho.erp.smc.exception.BizErrorCode;
import com.renpho.erp.smc.infrastructure.constants.UserConstants;
import com.renpho.erp.smc.infrastructure.persistence.dto.UserMenuPermissionDto;
import com.renpho.erp.smc.infrastructure.persistence.mapper.*;
import com.renpho.erp.smc.infrastructure.persistence.po.*;
import com.renpho.erp.smc.infrastructure.persistence.po.transformer.RoleTransformer;
import com.renpho.erp.smc.infrastructure.persistence.service.RoleDataPermissionServiceImpl;
import com.renpho.erp.smc.infrastructure.persistence.service.RoleFieldPermissionServiceImpl;
import com.renpho.erp.smc.infrastructure.persistence.service.RoleMenuServiceImpl;
import com.renpho.erp.smc.infrastructure.persistence.service.RoleUserServiceImpl;
import com.renpho.karma.exception.ErrorCodeException;
import com.renpho.karma.json.JSONKit;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 角色仓储实现.
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class RoleRepositoryImpl implements RoleRepository {

	private final RoleMapper roleMapper;

	private final RoleUserMapper roleUserMapper;

	private final RoleMenuMapper roleMenuMapper;

	private final RoleDataPermissionMapper roleDataPermissionMapper;

	private final RoleFieldPermissionMapper roleFieldPermissionMapper;

	private final MenuInfoMapper menuInfoMapper;

	private final DepartmentUserMapper departmentUserMapper;

	private final DepartmentMapper departmentMapper;

	private final OumUserInfoMapper userInfoMapper;

	private final RoleUserServiceImpl roleUserService;

	private final RoleMenuServiceImpl roleMenuService;

	private final RoleDataPermissionServiceImpl roleDataPermissionService;

	private final RoleFieldPermissionServiceImpl roleFieldPermissionService;

	private final RoleLanguageMapper roleLanguageMapper;

	// region 角色

	@Override
	public void checkDuplication(Role role) {
		for (MultiLanguage multiLanguage : role.getNames()) {
			String lang = multiLanguage.getLanguage();
			String name = multiLanguage.getName();
			LambdaQueryWrapper<RoleLanguagePO> queryWrapper = new LambdaQueryWrapper<>();
			if (Objects.isNull(role.getId())) {
				queryWrapper.eq(RoleLanguagePO::getLanguage, lang).eq(RoleLanguagePO::getName, name);
			}
			else {
				queryWrapper.eq(RoleLanguagePO::getLanguage, lang)
					.eq(RoleLanguagePO::getName, name)
					.ne(RoleLanguagePO::getRoleId, role.getId().getId());
			}
			Long count = this.roleLanguageMapper.selectCount(queryWrapper);
			if (count > 0) {
				throw new ErrorCodeException(BizErrorCode.ROLE_NAME_FOUND, name);
			}
		}
	}

	@Override
	public Integer saveRole(Role role) {
		RolePO rolePO = RoleTransformer.INSTANCE.toRolePO(role);
		// 状态：0 禁用；1 启用
		rolePO.setStatus(1);
		this.roleMapper.insert(rolePO);
		// 新增多语言
		List<RoleLanguagePO> roleLanguagePOList = RoleTransformer.INSTANCE.toRoleLanguagePOList(role.getNames(), rolePO.getId());
		for (RoleLanguagePO roleLanguagePO : roleLanguagePOList) {
			this.roleLanguageMapper.insert(roleLanguagePO);
		}
		return rolePO.getId();
	}

	@Override
	public Integer copyAdd(Role role) {
		// 新增角色
		RolePO rolePO = RoleTransformer.INSTANCE.toRolePO(role);
		rolePO.setId(null);
		// 状态：0 禁用；1 启用
		rolePO.setStatus(1);
		this.roleMapper.insert(rolePO);
		Integer roleId = rolePO.getId();
		// 新增多语言
		List<RoleLanguagePO> roleLanguagePOList = RoleTransformer.INSTANCE.toRoleLanguagePOList(role.getNames(), roleId);
		for (RoleLanguagePO roleLanguagePO : roleLanguagePOList) {
			this.roleLanguageMapper.insert(roleLanguagePO);
		}
		// 菜单权限
		List<Role.RoleMenu> roleMenuList = role.getRoleMenuList();
		if (CollectionUtils.isEmpty(roleMenuList)) {
			return roleId;
		}
		List<RoleMenuPO> menuList = new ArrayList<>();
		List<RoleDataPermissionPO> dataPermissionList = new ArrayList<>();
		// 构建菜单权限和数据权限(默认为我和我的下属)
		this.buildRoleMenuAndDataPermission(roleMenuList, roleId, menuList, dataPermissionList);
		// 入库
		this.roleMenuService.saveBatch(menuList, 100);
		this.roleDataPermissionService.saveBatch(dataPermissionList, 100);
		return roleId;
	}

	/**
	 * 构建菜单权限和数据权限(默认为我和我的下属)
	 */
	private void buildRoleMenuAndDataPermission(List<Role.RoleMenu> roleMenuList, Integer roleId, List<RoleMenuPO> menuList,
			List<RoleDataPermissionPO> dataPermissionList) {
		roleMenuList.forEach(roleMenu -> {
			Integer menuId = roleMenu.getMenuId();
			RoleMenuPO roleMenuPO = new RoleMenuPO();
			roleMenuPO.setRoleId(roleId);
			roleMenuPO.setMenuId(menuId);
			menuList.add(roleMenuPO);
			// 数据权限,默认为我和我的下属
			RoleDataPermissionPO dataPermission = new RoleDataPermissionPO();
			dataPermission.setRoleId(roleId);
			dataPermission.setMenuId(menuId);
			// 1 本人及下级
			dataPermission.setScopeType(1);
			// 若scope_type=1，set_type=0，biz_value=0
			dataPermission.setSetType(0);
			dataPermission.setBizValue(0);
			dataPermissionList.add(dataPermission);
		});
	}

	@Override
	public void updateRole(Role role) {
		RolePO rolePO = RoleTransformer.INSTANCE.toRolePO(role);
		this.roleMapper.updateById(rolePO);

		List<MultiLanguage> names = role.getNames();
		List<RoleLanguagePO> roleLanguagePOList = RoleTransformer.INSTANCE.toRoleLanguagePOList(role.getNames(), rolePO.getId());

		for (RoleLanguagePO roleLanguagePO : roleLanguagePOList) {
			RoleLanguagePO currentPO = this.roleLanguageMapper.selectOne(Wrappers.<RoleLanguagePO> lambdaQuery()
				.eq(RoleLanguagePO::getRoleId, roleLanguagePO.getRoleId())
				.eq(RoleLanguagePO::getLanguage, roleLanguagePO.getLanguage()));
			if (Objects.isNull(currentPO)) {
				this.roleLanguageMapper.insert(roleLanguagePO);
			}
			else {
				roleLanguagePO.setId(currentPO.getId());
				this.roleLanguageMapper.updateById(roleLanguagePO);
			}
		}
		List<String> languageExistList = names.stream().map(MultiLanguage::getLanguage).collect(Collectors.toList());
		LambdaQueryWrapper<RoleLanguagePO> deleteQueryWrapper = Wrappers.lambdaQuery(RoleLanguagePO.class)
			.eq(RoleLanguagePO::getRoleId, rolePO.getId())
			.notIn(RoleLanguagePO::getLanguage, languageExistList);
		this.roleLanguageMapper.delete(deleteQueryWrapper);
	}

	@Override
	public Role findRoleById(Role.RoleID roleId) {
		if (Objects.isNull(roleId)) {
			return null;
		}
		return RoleTransformer.INSTANCE.toRole(this.roleMapper.selectById(roleId.getId()));
	}

	@Override
	public void deleteRole(Integer id) {
		this.roleMapper.deleteById(id);
		this.roleLanguageMapper.delete(Wrappers.<RoleLanguagePO> lambdaQuery().eq(RoleLanguagePO::getRoleId, id));
	}

	// endregion

	// region 角色用户

	@Override
	public void findRoleUserByRoleId(Role role) {
		Role.RoleID roleId = role.getId();
		if (Objects.isNull(roleId)) {
			return;
		}
		List<RoleUserPO> roleUserList = this.roleUserMapper.listByRoleId(roleId.getId());
		Set<Integer> userIdSet = roleUserList.stream().map(RoleUserPO::getUserId).collect(Collectors.toSet());
		role.setUserIdSet(userIdSet);
	}

	@Override
	public void saveRoleUserList(Role role) {
		Set<Integer> userIdSet = role.getUserIdSet();
		Role.RoleID roleId = role.getId();
		if (CollectionUtils.isEmpty(userIdSet) || Objects.isNull(roleId)) {
			return;
		}
		// DB中角色已存在的用户
		List<RoleUserPO> existList = this.roleUserMapper.listByRoleId(roleId.getId());
		Set<Integer> existUserIdSet = existList.stream().map(RoleUserPO::getUserId).collect(Collectors.toSet());
		// 角色需要新增的用户
		userIdSet = userIdSet.stream().filter(u -> !existUserIdSet.contains(u)).collect(Collectors.toSet());
		role.setUserIdSet(userIdSet);
		// 构建数据
		List<RoleUserPO> roleUserList = new ArrayList<>();
		userIdSet.forEach(userId -> {
			RoleUserPO roleUser = new RoleUserPO();
			roleUser.setRoleId(roleId.getId());
			roleUser.setUserId(userId);
			roleUserList.add(roleUser);
		});
		// 入库
		if (CollectionUtils.isNotEmpty(roleUserList)) {
			this.roleUserService.saveBatch(roleUserList, 100);
		}
	}

	@Override
	public Set<Integer> findRoleUserIdByIds(Set<Integer> ids, Role role) {
		List<RoleUserPO> roleUserList = this.roleUserMapper.selectBatchIds(ids);
		Set<Integer> userIdSet = roleUserList.stream().map(RoleUserPO::getUserId).collect(Collectors.toSet());
		role.setUserIdSet(userIdSet);
		// 角色id
		roleUserList.stream().map(RoleUserPO::getRoleId).map(Role.RoleID::of).findFirst().ifPresent(role::setId);
		return roleUserList.stream().map(RoleUserPO::getId).collect(Collectors.toSet());
	}

	@Override
	public void batchDeleteRoleUser(Set<Integer> ids) {
		this.roleUserMapper.deleteBatchIds(ids);
	}

	// endregion

	// region 角色菜单

	@Override
	public void saveRoleMenuList(List<Role.RoleMenu> roleMenuList, Role.RoleID roleId) {
		if (Objects.isNull(roleId)) {
			return;
		}
		Set<Integer> menuIdSet = roleMenuList.stream().map(Role.RoleMenu::getMenuId).collect(Collectors.toSet());
		// 旧的角色菜单列表
		List<RoleMenuPO> oldRoleMenuList = this.roleMenuMapper.listByRoleId(roleId.getId());
		Set<Integer> oldMenuIdSet = oldRoleMenuList.stream().map(RoleMenuPO::getMenuId).collect(Collectors.toSet());
		// 当前需要删除的角色菜单id集合（RoleMenuPO.id）
		Set<Integer> deleteMenuIdSet = oldRoleMenuList.stream()
			.filter(oldMenu -> !menuIdSet.contains(oldMenu.getMenuId()))
			.map(RoleMenuPO::getId)
			.collect(Collectors.toSet());
		// 当前角色需要新增的菜单id集合（RoleMenuPO.menuId）
		Set<Integer> addMenuIdSet = menuIdSet.stream().filter(menuId -> !oldMenuIdSet.contains(menuId)).collect(Collectors.toSet());
		// 构建PO列表（新增）
		List<RoleMenuPO> addRoleMenuList = this.buildRoleMenuListOfAdd(roleId, addMenuIdSet);
		// DB操作
		if (CollectionUtils.isNotEmpty(deleteMenuIdSet)) {
			this.roleMenuMapper.deleteBatchIds(deleteMenuIdSet);
		}
		if (CollectionUtils.isNotEmpty(addRoleMenuList)) {
			this.roleMenuService.saveBatch(addRoleMenuList, 100);
		}
	}

	/**
	 * 构建PO列表（新增）
	 */
	private @NotNull List<RoleMenuPO> buildRoleMenuListOfAdd(Role.RoleID roleId, Set<Integer> addMenuIdSet) {
		List<RoleMenuPO> roleMenuList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(addMenuIdSet)) {
			addMenuIdSet.forEach(menuId -> {
				RoleMenuPO roleMenu = new RoleMenuPO();
				roleMenu.setRoleId(roleId.getId());
				roleMenu.setMenuId(menuId);
				roleMenuList.add(roleMenu);
			});
		}
		return roleMenuList;
	}

	@Override
	public void findRoleMenuListByRoleId(Role role) {
		Role.RoleID roleId = role.getId();
		if (Objects.isNull(roleId)) {
			return;
		}
		List<RoleMenuPO> roleMenuList = this.roleMenuMapper.listByRoleId(roleId.getId());
		role.setRoleMenuList(RoleTransformer.INSTANCE.toRoleMenuList(roleMenuList));
	}

	@Override
	public Role findRoleMenuByRoleId(Integer roleId) {
		RolePO rolePO = this.roleMapper.selectById(roleId);
		if (Objects.isNull(rolePO)) {
			return null;
		}
		Role role = RoleTransformer.INSTANCE.toRole(rolePO);
		List<RoleMenuPO> roleMenuList = this.roleMenuMapper.listByRoleId(roleId);
		List<RoleDataPermissionPO> roleDataPermissionList = this.roleDataPermissionMapper.listByRoleId(roleId);
		// Map<菜单id，数据权限列表
		Map<Integer, List<RoleDataPermissionPO>> dataPermissionMap = roleDataPermissionList.stream()
			.collect(Collectors.groupingBy(RoleDataPermissionPO::getMenuId));
		// 菜单数据权限
		List<Role.RoleMenu> roleMenus = new ArrayList<>();
		roleMenuList.forEach(rm -> {
			Integer menuId = rm.getMenuId();
			Role.RoleMenu roleMenu = new Role.RoleMenu();
			roleMenu.setMenuId(menuId);
			List<RoleDataPermissionPO> dataPermissionList = dataPermissionMap.get(menuId);
			if (CollectionUtils.isNotEmpty(dataPermissionList)) {
				Role.RoleDataPermission roleDataPermission = new Role.RoleDataPermission();
				dataPermissionList.stream().map(RoleDataPermissionPO::getSetType).findFirst().ifPresent(roleDataPermission::setSetType);
				dataPermissionList.stream().map(RoleDataPermissionPO::getScopeType).findFirst().ifPresent(roleDataPermission::setScopeType);
				Set<Integer> bizValueSet = dataPermissionList.stream().map(RoleDataPermissionPO::getBizValue).collect(Collectors.toSet());
				if (Integer.valueOf(1).equals(roleDataPermission.getSetType())) {
					roleDataPermission.setDepartmentIdSet(bizValueSet);
				}
				else {
					roleDataPermission.setUserIdSet(bizValueSet);
				}
				roleMenu.setDataPermission(roleDataPermission);
			}
			roleMenus.add(roleMenu);
		});
		role.setRoleMenuList(roleMenus);
		return role;
	}

	// endregion

	// region 角色数据权限

	@Override
	public void saveRoleDataPermissionList(List<Role.RoleMenu> roleMenuList, Role.RoleID roleId) {
		if (Objects.isNull(roleId)) {
			return;
		}
		Set<Integer> menuIdSet = roleMenuList.stream().map(Role.RoleMenu::getMenuId).collect(Collectors.toSet());
		// Map<菜单id，数据权限>
		Map<Integer, Role.RoleDataPermission> dataPermissionMap = roleMenuList.stream()
			.filter(rm -> Objects.nonNull(rm.getDataPermission()))
			.collect(Collectors.toMap(Role.RoleMenu::getMenuId, Role.RoleMenu::getDataPermission, (x1, x2) -> x1));

		List<MenuInfoPO> menuList = this.menuInfoMapper.selectBatchIds(menuIdSet);
		Map<Integer, MenuInfoPO> menuMap = menuList.stream().collect(Collectors.toMap(MenuInfoPO::getId, Function.identity()));
		// 构建数据权限PO列表
		List<RoleDataPermissionPO> dataPermissionList = this.buildDataPermissionList(roleMenuList, roleId, menuMap, dataPermissionMap);
		// 旧数据权限列表
		List<RoleDataPermissionPO> oldDataPermissionList = this.roleDataPermissionMapper.listByRoleId(roleId.getId());
		// 新旧数据不太好对比，直接删除再增加吧
		Set<Integer> oldIdSet = oldDataPermissionList.stream().map(RoleDataPermissionPO::getId).collect(Collectors.toSet());
		if (CollectionUtils.isNotEmpty(oldIdSet)) {
			this.roleDataPermissionMapper.deleteBatchIds(oldIdSet);
		}
		if (CollectionUtils.isNotEmpty(dataPermissionList)) {
			this.roleDataPermissionService.saveBatch(dataPermissionList, 100);
		}
	}

	/**
	 * 构建数据权限PO列表
	 */
	private List<RoleDataPermissionPO> buildDataPermissionList(List<Role.RoleMenu> roleMenuList, Role.RoleID roleId,
			Map<Integer, MenuInfoPO> menuMap, Map<Integer, Role.RoleDataPermission> dataPermissionMap) {
		List<RoleDataPermissionPO> dataPermissionList = new ArrayList<>();
		roleMenuList.forEach(roleMenu -> {
			MenuInfoPO menu = menuMap.get(roleMenu.getMenuId());
			// 菜单or目录，不保存数据权限
			if (Objects.isNull(menu) || UserConstants.TYPE_MENU.equals(menu.getMenuType())
					|| UserConstants.TYPE_DIR.equals(menu.getMenuType())) {
				return;
			}
			// 按钮
			if (UserConstants.TYPE_BUTTON.equals(menu.getMenuType())) {
				Role.RoleDataPermission dataPermission = roleMenu.getDataPermission();
				// 按钮数据权限为空，则取父级菜单的数据权限
				if (Objects.isNull(dataPermission)) {
					Role.RoleDataPermission parentDataPermission = dataPermissionMap.get(menu.getParentId());
					// 父级也为空，跳出
					if (Objects.isNull(parentDataPermission)) {
						return;
					}
					dataPermission = parentDataPermission;
				}
				// 数据范围类型，1 本人及下级、2 自定义
				switch (dataPermission.getScopeType()) {
					// 1 本人及下级
					case 1:
						// 构建角色数据权限PO（若scope_type=1，set_type=0，biz_value=0）
						this.buildDataPermission(roleId, dataPermissionList, roleMenu, 0, Sets.newHashSet(0), 1);
						break;
					// 2 自定义
					case 2:
						// 构建角色数据权限（自定义）
						this.buildDataPermissionForCustom(roleId, roleMenu, dataPermission, dataPermissionList);
						break;
					default:
						break;
				}
			}
		});
		return dataPermissionList;
	}

	/**
	 * 构建角色数据权限（自定义）
	 */
	private void buildDataPermissionForCustom(Role.RoleID roleId, Role.RoleMenu roleMenu, Role.RoleDataPermission dataPermission,
			List<RoleDataPermissionPO> dataPermissionList) {
		// 设置类型，1 按部门、2 按员工
		Integer setType = dataPermission.getSetType();
		switch (dataPermission.getSetType()) {
			case 1:
				// 部门id集合
				Set<Integer> departmentIdSet = dataPermission.getDepartmentIdSet();
				// 构建角色数据权限PO
				this.buildDataPermission(roleId, dataPermissionList, roleMenu, setType, departmentIdSet, 2);
				return;
			case 2:
				// 用户id集合
				Set<Integer> userIdSet = dataPermission.getUserIdSet();
				// 构建角色数据权限PO
				this.buildDataPermission(roleId, dataPermissionList, roleMenu, setType, userIdSet, 2);
				break;
			default:
				break;
		}
	}

	/**
	 * 构建角色数据权限PO
	 */
	private void buildDataPermission(Role.RoleID roleId, List<RoleDataPermissionPO> dataPermissionList, Role.RoleMenu roleMenu,
			Integer setType, Set<Integer> bizValueSet, Integer scopeType) {
		if (CollectionUtils.isNotEmpty(bizValueSet)) {
			bizValueSet.forEach(bizValue -> {
				RoleDataPermissionPO dataPermission = new RoleDataPermissionPO();
				dataPermission.setRoleId(roleId.getId());
				dataPermission.setMenuId(roleMenu.getMenuId());
				dataPermission.setScopeType(scopeType);
				dataPermission.setSetType(setType);
				dataPermission.setBizValue(bizValue);
				dataPermissionList.add(dataPermission);
			});
		}
	}

	@Override
	public List<UserDataPermission> findUserDataPermissionByUserIdSet(Set<Integer> userIdSet) {
		log.info("查询用户：{}数据权限", userIdSet);
		if (CollectionUtils.isEmpty(userIdSet)) {
			return new ArrayList<>();
		}
		// 用户菜单数据权限dto
		UserMenuPermissionDto userMenuPermissionDto = new UserMenuPermissionDto();
		// 用户集合的角色列表
		List<RoleUserPO> userRoleList = this.roleUserMapper.listByUserIdSet(userIdSet);
		if (CollectionUtils.isEmpty(userRoleList)) {
			return new ArrayList<>();
		}
		// Map<用户id, 角色id集合>
		Map<Integer, Set<Integer>> userRoleMap = userRoleList.stream()
			.collect(Collectors.groupingBy(RoleUserPO::getUserId, Collectors.mapping(RoleUserPO::getRoleId, Collectors.toSet())));
		userMenuPermissionDto.setUserRoleMap(userRoleMap);

		Set<Integer> allRoleIdSet = userRoleMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
		// 角色菜单列表
		List<RoleMenuPO> roleMenuList = this.roleMenuMapper.listByRoleIdSet(allRoleIdSet);
		if (CollectionUtils.isEmpty(roleMenuList)) {
			return new ArrayList<>();
		}
		// 角色数据权限列表
		List<RoleDataPermissionPO> roleDataPermissionList = this.roleDataPermissionMapper.listByRoleIdSet(allRoleIdSet);
		// Map<角色id, 菜单id集合>
		Map<Integer, Set<Integer>> roleMenuMap = roleMenuList.stream()
			.collect(Collectors.groupingBy(RoleMenuPO::getRoleId, Collectors.mapping(RoleMenuPO::getMenuId, Collectors.toSet())));
		userMenuPermissionDto.setRoleMenuMap(roleMenuMap);

		Set<Integer> menuIdSet = roleMenuList.stream().map(RoleMenuPO::getMenuId).collect(Collectors.toSet());
		List<MenuInfoPO> menuInfoList = this.menuInfoMapper.selectBatchIds(menuIdSet);
		// Map<角色id, 菜单>
		Map<Integer, MenuInfoPO> menuMap = menuInfoList.stream().collect(Collectors.toMap(MenuInfoPO::getId, Function.identity()));
		userMenuPermissionDto.setMenuMap(menuMap);

		// Map<角色id, 数据权限列表>
		Map<Integer, List<RoleDataPermissionPO>> roleDataPermissionMap = roleDataPermissionList.stream()
			.collect(Collectors.groupingBy(RoleDataPermissionPO::getRoleId));
		userMenuPermissionDto.setRoleDataPermissionMap(roleDataPermissionMap);
		// 部门id集合
		Set<Integer> departmentIdSet = roleDataPermissionList.stream()
			.filter(d -> Integer.valueOf(1).equals(d.getSetType()))
			.map(RoleDataPermissionPO::getBizValue)
			.collect(Collectors.toSet());
		// 用户部门列表
		List<DepartmentUserPO> userDepartmentList = this.departmentUserMapper.listByUserIdSet(userIdSet);
		departmentIdSet.addAll(userDepartmentList.stream().map(DepartmentUserPO::getDepartmentId).collect(Collectors.toSet()));
		// 查所有部门（只返回id,parentId和managerId），数据量大了再优化为分批查询
		List<DepartmentPO> departmentList = this.departmentMapper.listIdAndParentIdAndManagerId();
		// 构建部门数据，Map<部门id，子部门id（包含子子部门）>
		Map<Integer, Set<Integer>> departmentIdMap = this.buildDepartmentIdMap(departmentIdSet, departmentList);
		userMenuPermissionDto.setDepartmentIdMap(departmentIdMap);
		// 数据权限涉及的所有部门
		Set<Integer> allDepartmentIdSet = departmentIdMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
		allDepartmentIdSet.addAll(departmentIdMap.keySet());
		// 部门用户列表
		List<DepartmentUserPO> departmentUserList = this.departmentUserMapper.listByDepartmentIdSet(allDepartmentIdSet);
		userMenuPermissionDto.setDepartmentUserList(departmentUserList);
		// 直属领导用户列表
		List<OumUserInfoPO> reportUserList = userInfoMapper.listReportUser();
		userMenuPermissionDto.setReportUserList(reportUserList);
		// 构建用户菜单数据权限列表
		List<UserDataPermission> userDataPermissionList = this.buildUserMenuPermissionList(userMenuPermissionDto);
		userDataPermissionList.forEach(userDataPermission -> log.info("查询用户：{}数据权限，结果集：{}", userDataPermission.getUserId(),
				JSONKit.toJSONString(userDataPermission.getMenuMap())));
		return userDataPermissionList;
	}

	public Set<Integer> findSubordinateUserIdSet(Integer userId, List<OumUserInfoPO> reportUserList) {
		Set<Integer> subordinateUserIdSet = new HashSet<>();
		if (Objects.isNull(userId)) {
			return subordinateUserIdSet;
		}
		reportUserList.forEach(userInfo -> {
			if (!userId.equals(userInfo.getReportUserId())) {
				return;
			}
			// 下级
			Integer subordinateUserId = userInfo.getId();
			subordinateUserIdSet.add(subordinateUserId);
			// 递归查找下下级
			subordinateUserIdSet.addAll(this.findSubordinateUserIdSet(subordinateUserId, reportUserList));
		});
		return subordinateUserIdSet;
	}

	@Override
	public List<UserDataPermission> findUserDataPermissionByDepartmentIdSet(Set<Integer> departmentIdSet) {
		// 查所有部门（只返回id,parentId和managerId），数据量大了再优化为分批查询
		List<DepartmentPO> departmentList = this.departmentMapper.listIdAndParentIdAndManagerId();
		Set<Integer> partentDepartmentIdSet = new HashSet<>();
		// 递归查找父部门
		departmentIdSet.forEach(departmentId -> this.findParentDepartmentId(departmentList, departmentId, partentDepartmentIdSet));
		departmentIdSet.addAll(partentDepartmentIdSet);
		// 按部门
		Set<Integer> roleIdSet = this.roleDataPermissionMapper.getRoleIdSetByTypeAndBizValue(2, 1, departmentIdSet);
		// 本人及下级（部门领导）
		Set<Integer> managerIdSet = departmentList.stream()
			.filter(d -> departmentIdSet.contains(d.getId()))
			.map(DepartmentPO::getManagerId)
			.filter(Objects::nonNull)
			.collect(Collectors.toSet());
		if (CollectionUtils.isEmpty(roleIdSet)) {
			return this.findUserDataPermissionByUserIdSet(managerIdSet);
		}
		Set<Integer> userIdSet = this.roleUserMapper.getByRoleIdSet(roleIdSet);
		userIdSet.addAll(managerIdSet);
		// 查询用户菜单数据权限列表
		return this.findUserDataPermissionByUserIdSet(userIdSet);
	}

	/**
	 * 递归查找父部门.
	 */
	private void findParentDepartmentId(List<DepartmentPO> departmentList, Integer departmentId, Set<Integer> parentDepartmentIdSet) {
		if (Objects.isNull(departmentId)) {
			return;
		}
		// 查找父部门
		departmentList.forEach(department -> {
			Integer parentId = department.getParentId();
			if (!departmentId.equals(department.getId()) || Objects.isNull(parentId)) {
				return;
			}
			// 添加
			parentDepartmentIdSet.add(parentId);
			// 递归查询父父级部门
			this.findParentDepartmentId(departmentList, parentId, parentDepartmentIdSet);
		});
	}

	/**
	 * 构建部门数据
	 */
	private Map<Integer, Set<Integer>> buildDepartmentIdMap(Set<Integer> departmentIdSet, List<DepartmentPO> departmentList) {
		// Map<部门id，子部门id（包含子子部门）>
		Map<Integer, Set<Integer>> departmentIdMap = new HashMap<>();
		if (CollectionUtils.isEmpty(departmentList)) {
			return departmentIdMap;
		}
		// Map<部门id，子部门id（不包含子子部门）>
		Map<Integer, Set<Integer>> departmentMap = departmentList.stream()
			.collect(Collectors.groupingBy(DepartmentPO::getParentId, Collectors.mapping(DepartmentPO::getId, Collectors.toSet())));
		departmentIdSet.forEach(departmentId -> {
			// 子部门id（包含子子部门）集合
			Set<Integer> allSubDepartmentIdSet = new HashSet<>();
			this.buildAllSubDepartmentIdSet(departmentId, allSubDepartmentIdSet, departmentMap);
			departmentIdMap.put(departmentId, allSubDepartmentIdSet);
		});
		return departmentIdMap;
	}

	/**
	 * 查询当前部门的所有子部门的信息（包含子子部门）
	 */
	private void buildAllSubDepartmentIdSet(Integer departmentId, Set<Integer> allSubDepartmentIdSet,
			Map<Integer, Set<Integer>> departmentMap) {
		Set<Integer> currentSubDepartmentIdSet = departmentMap.get(departmentId);
		if (CollectionUtils.isEmpty(currentSubDepartmentIdSet)) {
			return;
		}
		allSubDepartmentIdSet.addAll(currentSubDepartmentIdSet);
		// 递归查询当前部门的子部门
		currentSubDepartmentIdSet.forEach(c -> this.buildAllSubDepartmentIdSet(c, allSubDepartmentIdSet, departmentMap));
	}

	/**
	 * 构建用户菜单数据权限列表
	 */
	private List<UserDataPermission> buildUserMenuPermissionList(UserMenuPermissionDto userMenuPermissionDto) {
		Map<Integer, Set<Integer>> userRoleMap = userMenuPermissionDto.getUserRoleMap();
		Map<Integer, Set<Integer>> roleMenuMap = userMenuPermissionDto.getRoleMenuMap();
		Map<Integer, MenuInfoPO> menuMap = userMenuPermissionDto.getMenuMap();
		Map<Integer, List<RoleDataPermissionPO>> roleDataPermissionMap = userMenuPermissionDto.getRoleDataPermissionMap();
		Map<Integer, Set<Integer>> departmentIdMap = userMenuPermissionDto.getDepartmentIdMap();
		List<DepartmentUserPO> departmentUserList = userMenuPermissionDto.getDepartmentUserList();
		List<OumUserInfoPO> reportUserList = userMenuPermissionDto.getReportUserList();
		// Map<部门id, 用户id集合>
		Map<Integer, Set<Integer>> departmentUserMap = departmentUserList.stream()
			.collect(Collectors.groupingBy(DepartmentUserPO::getDepartmentId,
					Collectors.mapping(DepartmentUserPO::getUserId, Collectors.toSet())));
		List<UserDataPermission> userDataPermissionList = new ArrayList<>();
		// 遍历
		userRoleMap.forEach((userId, roleIdSet) -> {
			UserDataPermission userDataPermission = new UserDataPermission();
			userDataPermission.setUserId(userId);
			// 查找我的下属和下下属
			Set<Integer> subordinateUserIdSet = this.findSubordinateUserIdSet(userId, reportUserList);
			// Map<菜单id, 数据权限的用户id集合>
			Map<String, Set<Integer>> menuUserMap = new HashMap<>();
			roleIdSet.forEach(roleId -> {
				// 当前角色拥有的菜单集合
				Set<Integer> menuIdSet = roleMenuMap.get(roleId);
				// 当前角色拥有的数据权限列表
				List<RoleDataPermissionPO> dataPermissionList = roleDataPermissionMap.get(roleId);
				if (CollectionUtils.isEmpty(menuIdSet) || CollectionUtils.isEmpty(dataPermissionList)) {
					return;
				}
				// Map<菜单id, 数据权限列表>
				Map<Integer, List<RoleDataPermissionPO>> menuDataPermissionMap = dataPermissionList.stream()
					.collect(Collectors.groupingBy(RoleDataPermissionPO::getMenuId));
				menuIdSet.forEach(menuId -> {
					List<RoleDataPermissionPO> menuDataPermissionList = menuDataPermissionMap.get(menuId);
					MenuInfoPO menu = menuMap.get(menuId);
					// 菜单和目录没有数据权限
					if (CollectionUtils.isEmpty(menuDataPermissionList) || Objects.isNull(menu)
							|| !UserConstants.TYPE_BUTTON.equals(menu.getMenuType())) {
						return;
					}
					Set<Integer> dataPermissionUserIdSet = new HashSet<>();
					// 当前用户也加上去（能查自己的数据）
					dataPermissionUserIdSet.add(userId);
					// 是否自定义
					boolean custom = menuDataPermissionList.stream().allMatch(d -> Integer.valueOf(2).equals(d.getScopeType()));
					if (custom) {
						// 自定义
						this.buildCustom(departmentIdMap, menuDataPermissionList, departmentUserMap, dataPermissionUserIdSet);
					}
					else {
						// 我和我的下属
						dataPermissionUserIdSet.addAll(subordinateUserIdSet);
					}
					// 权限标识符
					String perms = menu.getPerms();
					// 用户拥有多个角色的场景，如果权限标识符已存在，往集合添加 用户id
					if (CollectionUtils.isNotEmpty(menuUserMap.get(perms))) {
						menuUserMap.get(perms).addAll(dataPermissionUserIdSet);
					}
					else {
						menuUserMap.put(perms, dataPermissionUserIdSet);
					}
				});
			});
			userDataPermission.setMenuMap(menuUserMap);
			userDataPermissionList.add(userDataPermission);
		});
		return userDataPermissionList;
	}

	/**
	 * 自定义
	 */
	private void buildCustom(Map<Integer, Set<Integer>> departmentIdMap, List<RoleDataPermissionPO> menuDataPermissionList,
			Map<Integer, Set<Integer>> departmentUserMap, Set<Integer> dataPermissionUserIdSet) {
		menuDataPermissionList.forEach(d -> {
			switch (d.getSetType()) {
				// 按部门
				case 1:
					// 构建当前部门的用户 及 子部门（包含子子部门）的用户
					this.buildDepartmentUserIdSet(departmentUserMap, departmentIdMap, d.getBizValue(), dataPermissionUserIdSet);
					break;
				// 按用户
				case 2:
					dataPermissionUserIdSet.add(d.getBizValue());
					break;
				default:
					break;
			}
		});
	}

	/**
	 * 构建当前部门的用户 及 子部门（包含子子部门）的用户
	 */
	private void buildDepartmentUserIdSet(Map<Integer, Set<Integer>> departmentUserMap, Map<Integer, Set<Integer>> departmentIdMap,
			Integer departmentId, Set<Integer> dataPermissionUserIdSet) {
		// 子部门id（包含子子部门）
		Set<Integer> allSubDepartmentId = departmentIdMap.get(departmentId);
		if (CollectionUtils.isNotEmpty(allSubDepartmentId)) {
			// 子部门（包含子子部门）的用户
			allSubDepartmentId.forEach(subDepartmentId -> Optional.ofNullable(departmentUserMap.get(subDepartmentId))
				.ifPresent(dataPermissionUserIdSet::addAll));
		}
		// 当前部门的用户
		Optional.ofNullable(departmentUserMap.get(departmentId)).ifPresent(dataPermissionUserIdSet::addAll);
	}

	// endregion

	// region 角色字段脱敏权限

	@Override
	public void saveRoleFieldPermission(List<Role.RoleFieldPermission> fieldPermissionList, Role.RoleID id) {
		Integer roleId = id.getId();
		if (Objects.isNull(roleId)) {
			return;
		}
		// DB中已存在的字段脱敏权限列表
		List<RoleFieldPermissionPO> oldFieldPermissionList = this.roleFieldPermissionMapper.listByRoleId(roleId);
		// Map<字段标识符, RoleFieldPermission>
		Map<String, RoleFieldPermissionPO> fieldPermissionMap = oldFieldPermissionList.stream()
			.collect(Collectors.toMap(RoleFieldPermissionPO::getName, Function.identity(), (x1, x2) -> x1));
		List<RoleFieldPermissionPO> updateList = new ArrayList<>();
		List<RoleFieldPermissionPO> addList = new ArrayList<>();
		// 构建修改和新增列表
		this.buildUpdateAndAddList(fieldPermissionList, fieldPermissionMap, updateList, roleId, addList);
		// 需要删除的id集合
		Set<String> fieldNameSet = fieldPermissionList.stream().map(Role.RoleFieldPermission::getName).collect(Collectors.toSet());
		Set<Integer> deleteIdSet = oldFieldPermissionList.stream()
			.filter(o -> !fieldNameSet.contains(o.getName()))
			.map(RoleFieldPermissionPO::getId)
			.collect(Collectors.toSet());
		// DB操作
		if (CollectionUtils.isNotEmpty(updateList)) {
			this.roleFieldPermissionService.updateBatchById(updateList, 100);
		}
		if (CollectionUtils.isNotEmpty(addList)) {
			this.roleFieldPermissionService.saveBatch(addList, 100);
		}
		if (CollectionUtils.isNotEmpty(deleteIdSet)) {
			this.roleFieldPermissionMapper.deleteBatchIds(deleteIdSet);
		}
	}

	/**
	 * 构建修改和新增列表
	 */
	private void buildUpdateAndAddList(List<Role.RoleFieldPermission> fieldPermissionList,
			Map<String, RoleFieldPermissionPO> fieldPermissionMap, List<RoleFieldPermissionPO> updateList, Integer roleId,
			List<RoleFieldPermissionPO> addList) {
		fieldPermissionList.forEach(f -> {
			RoleFieldPermissionPO oldFieldPermission = fieldPermissionMap.get(f.getName());
			RoleFieldPermissionPO fieldPermission = new RoleFieldPermissionPO();
			if (Objects.nonNull(oldFieldPermission)) {
				fieldPermission.setId(oldFieldPermission.getId());
				fieldPermission.setStatus(f.getStatus());
				updateList.add(fieldPermission);
			}
			else {
				fieldPermission.setRoleId(roleId);
				fieldPermission.setName(f.getName());
				fieldPermission.setStatus(f.getStatus());
				addList.add(fieldPermission);
			}
		});
	}

	@Override
	public List<UserFieldPermission> findUserMenuPermissionListByUserIdSet(Set<Integer> userIdSet) {
		if (CollectionUtils.isEmpty(userIdSet)) {
			return new ArrayList<>();
		}
		// 用户集合的角色列表
		List<RoleUserPO> userRoleList = this.roleUserMapper.listByUserIdSet(userIdSet);
		if (CollectionUtils.isEmpty(userRoleList)) {
			return new ArrayList<>();
		}
		Set<Integer> allRoleIdSet = userRoleList.stream().map(RoleUserPO::getRoleId).collect(Collectors.toSet());
		List<RoleFieldPermissionPO> allFieldPermissionList = this.roleFieldPermissionMapper.listByRoleIdSet(allRoleIdSet);
		// Map<角色id, 字段脱敏权限列表>
		Map<Integer, List<RoleFieldPermissionPO>> roleFieldPermissionMap = allFieldPermissionList.stream()
			.collect(Collectors.groupingBy(RoleFieldPermissionPO::getRoleId));
		// Map<用户id, 角色id集合>
		Map<Integer, Set<Integer>> userRoleMap = userRoleList.stream()
			.collect(Collectors.groupingBy(RoleUserPO::getUserId, Collectors.mapping(RoleUserPO::getRoleId, Collectors.toSet())));
		// 构建用户字段脱敏权限列表
		return this.buildUserFieldPermissionList(userRoleMap, roleFieldPermissionMap);
	}

	@Override
	public void setRoleLanguage(Role role) {
		if (role.getId() != null) {
			List<RoleLanguagePO> roleLanguagePOS = roleLanguageMapper
				.selectList(Wrappers.<RoleLanguagePO> lambdaQuery().eq(RoleLanguagePO::getRoleId, role.getId().getId()));
			ArrayList<MultiLanguage> multiLanguages = new ArrayList<>();
			for (RoleLanguagePO roleLanguagePO : roleLanguagePOS) {
				MultiLanguage multiLanguage = new MultiLanguage();
				multiLanguage.setLanguage(roleLanguagePO.getLanguage());
				multiLanguage.setName(roleLanguagePO.getName());
				multiLanguages.add(multiLanguage);
			}
			role.setNames(multiLanguages);
		}
	}

	private @NotNull List<UserFieldPermission> buildUserFieldPermissionList(Map<Integer, Set<Integer>> userRoleMap,
			Map<Integer, List<RoleFieldPermissionPO>> roleFieldPermissionMap) {
		List<UserFieldPermission> userFieldPermissionList = new ArrayList<>();
		userRoleMap.forEach((userId, roleIdSet) -> {
			UserFieldPermission userFieldPermission = new UserFieldPermission();
			userFieldPermission.setUserId(userId);
			// Map<key为敏感字段标识符，value为是否可见,true可见,false不可见>
			Map<String, Boolean> fieldMap = new HashMap<>();
			roleIdSet.forEach(roleId -> {
				List<RoleFieldPermissionPO> fieldPermissionList = roleFieldPermissionMap.get(roleId);
				if (CollectionUtils.isEmpty(fieldPermissionList)) {
					return;
				}
				fieldPermissionList.forEach(fieldPermission -> {
					// 字段名
					String fieldName = fieldPermission.getName();
					boolean fieldExist = fieldMap.containsKey(fieldName);
					if (fieldExist) {
						// 用户属于多个角色,敏感字段已存在
						Boolean visible = fieldMap.get(fieldName);
						// 多个角色,只要有一个可见,就设定为可见,如果原来的值为不可见,才更新value
						if (Boolean.FALSE.equals(visible)) {
							fieldMap.put(fieldName, Integer.valueOf(1).equals(fieldPermission.getStatus()));
						}
					}
					else {
						fieldMap.put(fieldName, Integer.valueOf(1).equals(fieldPermission.getStatus()));
					}
				});
			});
			userFieldPermission.setFieldMap(fieldMap);
			userFieldPermissionList.add(userFieldPermission);
		});
		return userFieldPermissionList;
	}

	// endregion

}
