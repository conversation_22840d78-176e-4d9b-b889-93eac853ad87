package com.renpho.erp.smc.infrastructure.persistence.po;

import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.erp.smc.domain.systemsetting.MultiLanguage;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;

import lombok.Data;

@Data
@TableName("oum_office_location")
public class OfficeLocationPO extends DefaultPO implements Serializable {

	// private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	// @TableId(type = IdType.AUTO)
	// private Integer id;

	/**
	 * 状态：0 禁用；1 启用
	 */
	// private Integer status;

	/**
	 * 创建人ID
	 */
	// private Integer createBy;

	/**
	 * 创建时间
	 */
	// private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	// private Integer updateBy;

	/**
	 * 更新时间
	 */
	// private LocalDateTime updateTime;

	/** 是否删除：0 否、1 是；默认 0 **/
	// private Integer isDeleted;

	@TableField(exist = false)
	private String statusName;

	@TableField(exist = false)
	private String updateName;

	@TableField(exist = false)
	private String language;

	@TableField(exist = false)
	private String name;

	@TableField(exist = false)
	private List<String> nameList;

	@TableField(exist = false)
	private String names;

	@TableField(exist = false)
	private List<MultiLanguage> multiLanguages;

	@TableField(exist = false)
	private String userCode;

	// @TableField(exist = false)
	// private Integer pageNum;

	// @TableField(exist = false)
	// private Integer pageSize;

}
