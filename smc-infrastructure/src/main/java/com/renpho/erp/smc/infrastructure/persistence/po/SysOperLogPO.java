package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;
import lombok.Data;

@Data
@TableName("sys_operate_log")
public class SysOperLogPO extends DefaultPO {

	/** 操作类型(要跟产品确定), 创建，编辑，删除，审核通过 **/
	private String businessType;

	/** 判断是什么业务模块，MENU 菜单，DICT 字典， PARAM参数 */
	private String businessModule;

	/** 操作模块 例如： OMS/菜单页面 */
	private String operationModule;

	/** 业务数据 */
	private String businessData;

	/** 业务id */
	private String bsId;

	/** 目标方法名 **/
	private String classMethod;

	/** 数据快照，旧和新{old, new} **/
	private String dataSnapt;

	/** 页面编码 **/
	private String docNo;

	/** 方法耗时（ms） **/
	private Integer executionTime;

	/** 操作ip **/
	private String ip;

	/** 备注 **/
	private String remark;

	/** 请求参数 **/
	private String reqBody;

	/** 请求类型（GET,POST） **/
	private String reqMethod;

	/** 返回结果 **/
	private String result;

	/** 系统模块 **/
	private String systemModule;

	/** 追踪编码 **/
	private String traceNo;

	/** 请求URI **/
	private String uri;

	/** 客户端类型（web、dingtalk-miniapp、app等） **/
	private String clientType;

}
