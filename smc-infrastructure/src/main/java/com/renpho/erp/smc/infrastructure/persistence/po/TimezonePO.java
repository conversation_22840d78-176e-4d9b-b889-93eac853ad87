package com.renpho.erp.smc.infrastructure.persistence.po;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;

import lombok.Data;

/**
 * 多时区表
 */
@Data
@TableName("sys_timezone")
public class TimezonePO extends DefaultPO {

	/**
	 * 洲名(英文)
	 */
	private String continentsEn;

	/**
	 * 洲名(中文)
	 */
	private String continentsCn;

	/**
	 * 国家名(英文)
	 */
	private String contryEn;

	/**
	 * 国家名(中文)
	 */
	private String contryCn;

	/**
	 * 城市名
	 */
	private String cityEn;

	private String cityCn;

	/**
	 * 时区
	 */
	private String timeZoneNo;

	/**
	 * 时区名
	 */
	private String timeZoneNm;

	/**
	 * 是否夏令时标记：0 否、1 是
	 */
	private String smrMk;

	/**
	 * 是否开启夏令时中：0 否、1 是
	 */
	private String smrStatus;

	/**
	 * 夏令时开始时间
	 */
	private LocalDateTime smrStrDatetime;

	/**
	 * 夏令时结束时间
	 */
	private LocalDateTime smrEndDatetime;

}