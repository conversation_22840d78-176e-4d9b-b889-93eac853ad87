package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@TableName("sys_menu_info")
public class MenuInfoPO extends DefaultPO {

	/**
	 * `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键ID', `parent_id` int(10) NOT NULL
	 * DEFAULT '0' COMMENT '父目录ID，0', `system_module` varchar(64) NOT NULL COMMENT
	 * '系统模块，', `menu_name` json NOT NULL COMMENT '菜单名称, 多言语', `menu_icon` varchar(255)
	 * NOT NULL COMMENT '菜单图标', `menu_type` varchar(64) NOT NULL COMMENT '菜单类型, 目录、菜单、按钮',
	 * `component_path` varchar(255) NOT NULL COMMENT '菜单路径', `route_address` varchar(255)
	 * NOT NULL COMMENT '路由地址', `route_params` varchar(255) NOT NULL COMMENT '路由参数',
	 * `perms` varchar(255) NOT NULL COMMENT '权限字符', `sort` int NOT NULL COMMENT
	 * '菜单排序，0,1,2', `status` int NOT NULL COMMENT '菜单状态，0=InActive,1=Active',
	 * `is_deleted` int(11) NOT NULL COMMENT '是否删除', `is_cached` int(11) NOT NULL COMMENT
	 * '是否缓存', `update_by` int NOT NULL COMMENT '更新的用户', `create_by` int NOT NULL COMMENT
	 * '创建的用户', `update_time` DATETIME NOT NULL COMMENT '更新时间', `create_time` DATETIME NOT
	 * NULL COMMENT '创建时间',
	 */

	private Integer parentId;

	private String systemModule;

	/**
	 * 多语言名称，map {"en":"Menu Management", "cn":"菜单管理"}
	 */
	private String multiLanguageNames;

	private String menuIcon;

	private String menuType;

	/**
	 * 组件路径
	 */
	private String componentPath;

	private String routeAddress;

	private String routeParams;

	private String perms;

	private Integer sort;

	/**
	 * 是否内链，1=否，0=是
	 */
	@TableField(exist = false)
	private Integer isFrame;

	private Integer isCached;

	private Integer isDeleted;

	@TableField(exist = false)
	private String language;

	@TableField(exist = false)
	private String name;

	@TableField(exist = false)
	private List<MenuInfoPO> children;

	@TableField(exist = false)
	private List<MenuInfoMultilanguagePO> names;

}
