package com.renpho.erp.smc.infrastructure.persistence.po;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;

import lombok.Data;

/**
 * @description: 用户信息表
 * @author: doctor
 * @date: 2024/09/13
 * @version: 1.0.0
 */
@Data
@TableName("oum_user_info")
public class OumUserInfoPO extends DefaultPO {

	private static final long serialVersionUID = 1L;

	/** 状态 **/
	public static final String STATUS_DICT = "staff_status";

	/** 类型 字典key */
	public static final String TYPE_DICT = "employ_type";

	/** 用户工号 **/
	private String code;

	/** 合同地点ID **/
	private Integer contractId;

	private Integer costcenterId;

	/** 公司ID **/
	private Integer corporationId;

	/** 主部门ID **/
	// private Integer departmentId;

	/** 邮箱 **/
	private String email;

	/** 用户性别，1 男、2 女、3 未知 **/
	@TableField(updateStrategy = FieldStrategy.ALWAYS)
	private Integer gender;

	/** 入职时间 **/
	private LocalDateTime hireTime;

	/** 公积金号 **/
	private String housingFundNo;

	/** 岗位添加时间 */
	private LocalDateTime positionAddedTime;

	private String officeZone;

	/** 用户姓名 **/
	private String name;

	/** 用户旧工号 **/
	private String oldCode;

	/** 手机号码 **/
	@TableField(updateStrategy = FieldStrategy.ALWAYS)
	private String phoneNo;

	/** 岗位ID **/
	private Integer positionId;

	/** 直属领导ID **/
	@TableField(updateStrategy = FieldStrategy.ALWAYS)
	private Integer reportUserId;

	/** 离职时间 **/
	@TableField(updateStrategy = FieldStrategy.ALWAYS)
	private LocalDateTime resignationTime;

	/** 社保电脑号 **/
	private String socialInsuranceNo;

	/**
	 * 全日制 Full-time Employment：FT
	 *
	 * 非全日制 Part-time Employment：PE
	 *
	 * 退休返聘 Re-employ after Retirement ：RE
	 *
	 * 兼职 Part-time：PT
	 *
	 * 劳务派遣 Dispatched：DI
	 *
	 * 劳务外包 Labor-outsourcing：LO
	 *
	 * 实习生 Internship：IN
	 *
	 * 服务外包 Service-outsourcing：SO
	 *
	 * 自由职业者 Freelance：FL
	 **/
	private String type;

}
