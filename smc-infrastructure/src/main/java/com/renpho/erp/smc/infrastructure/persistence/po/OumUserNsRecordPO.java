package com.renpho.erp.smc.infrastructure.persistence.po;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.StatusPO;

import lombok.Data;

/**
 * @description: 用户与NS用户记录表
 * @author: doctor
 * @date: 2024/09/18
 * @version: 1.0.0
 */
@Data
@TableName("oum_user_ns_record")
public class OumUserNsRecordPO extends StatusPO<Long, Integer, Integer, LocalDateTime, Integer> {

	private static final long serialVersionUID = 1L;

	/** status */
	public static final String STATUS_DICT = "nsrecord_status";

	/** 用户ID **/
	private Integer userId;

	/** NS ID **/
	private String nsId;

	/** NS用户名称 **/
	private String nsUserName;

	/** NS公司名称 **/
	private String nsCorporationName;

	/** 是否默认：0 否、1 是；默认 0 **/
	private Integer isDefaulted;

	/** 失败原因 **/
	private String reason;

}
