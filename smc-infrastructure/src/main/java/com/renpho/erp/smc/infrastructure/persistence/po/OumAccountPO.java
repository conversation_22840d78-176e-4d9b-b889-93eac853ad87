package com.renpho.erp.smc.infrastructure.persistence.po;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;

import lombok.Data;

/**
 * @description: 用户账号表
 * @author: doctor
 * @date: 2024/09/13
 * @version: 1.0.0
 */
@Data
@TableName("oum_account")
public class OumAccountPO extends DefaultPO {

	private static final long serialVersionUID = 1L;

	/** 账号状态 **/
	public static final String STATUS_DICT = "account_status";

	/** 账号 **/
	private String account;

	private String email;

	/** 是否初始化密码，0 否、1 是 **/
	private Integer isInitializedPwd;

	/** 最新更新密码的时间 **/
	private LocalDateTime lastUpdatedPwdTime;

	/** 密码 **/
	private String pwd;

	/** 类型，1 账号、2 手机号、3 邮箱 **/
	private String mobile;

	/** 用户ID **/
	private Integer userId;

}
