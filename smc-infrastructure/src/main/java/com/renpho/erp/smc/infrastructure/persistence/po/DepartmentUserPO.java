package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@TableName("oum_department_user")
public class DepartmentUserPO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> {

	/**
	 * 部门ID
	 */
	private Integer departmentId;

	/**
	 * 是否主部门，0 否、1 是
	 */
	private Boolean isPrimary;

	/**
	 * 用户ID
	 */
	private Integer userId;

	@TableField(exist = false)
	private String userName;

	@TableField(exist = false)
	private Integer departmentStatus;

	@TableField(exist = false)
	private String departmentName;

	@TableField(exist = false)
	private String managerName;

	@TableField(exist = false)
	private String email;

	@TableField(exist = false)
	private Integer accountStatus;

	@TableField(exist = false)
	private Integer status;

	@TableField(exist = false)
	private String userCode;

	@TableField(exist = false)
	private String departmentCode;

	@TableField(exist = false)
	private String positionName;

	@TableField(exist = false)
	private String parentCode;

	@TableField(exist = false)
	private String parentName;

	@TableField(exist = false)
	private Integer levelLable;

	@TableField(exist = false)
	private List<Integer> positionIds;

	@TableField(exist = false)
	private List<Integer> userIds;

}
