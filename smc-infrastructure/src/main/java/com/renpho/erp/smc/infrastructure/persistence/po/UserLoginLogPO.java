package com.renpho.erp.smc.infrastructure.persistence.po;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;

import lombok.Data;

/**
 * @description: 用户登录日志
 */
@Data
@TableName("oum_user_login_log")
public class UserLoginLogPO extends DefaultPO {

	private static final long serialVersionUID = 1L;

	/** 失败多少次，就出现限制登录 */
	public static final String ERROR_TIMES = "ERROR_TIMES";

	/** 新设备限制 */
	public static final String LIMIT_NEW_DEVICE = "LIMIT_NEW_DEVICE";

	/**  **/
	private Long userId;

	/** 用户名字 **/
	private String username;

	/** 登录时间 **/
	private LocalDateTime loginTime;

	/** 登录ip **/
	private String loginIp;

	/** 登录地址 **/
	private String loginAddr;

	/** 浏览器信息，浏览器，版本，指纹 **/
	private String browserInfo;

	/** 操作系统信息，{分辨率，系统OS} **/
	private String sysosInfo;

	/** 登录状态 **/
	private String loginStatus;

	/** 登录信息 msg **/
	private String loginInfo;

	/** 登录类型 */
	private String loginType;

	/** 登录异常 **/
	private String loginError;

	private String employeeName;

	private String employeeID;

	private String browserSystem;

	private String browserFingerprint;

	private String operatingSystem;

	private String screenResolution;

}
