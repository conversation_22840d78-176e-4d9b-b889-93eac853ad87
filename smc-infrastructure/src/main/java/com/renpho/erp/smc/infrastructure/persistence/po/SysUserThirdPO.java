package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;
import lombok.Data;

@Data
@TableName("sys_user_third")
public class SysUserThirdPO extends DefaultPO {

	/** 第三方平台头像 **/
	private String avatarUrl;

	/** 部门id **/
	private Long deptId;

	/** 手机号 **/
	private String mobile;

	/** 手机号 **/
	private String email;

	/** 第三方平台的用户名称 **/
	private String nick;

	/** 第三方平台openId **/
	private String openId;

	/** 第三方平台：dingtalk **/
	private String platform;

	/** 刷新token **/
	private String refreshToken;

	/** 系统用户的id **/
	private Integer sysUserId;

	/** 第三方平台id **/
	private String unionid;

	/** 钉钉用户id **/
	private String userid;

}
