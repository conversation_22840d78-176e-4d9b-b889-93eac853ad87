package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.erp.smc.domain.systemsetting.MultiLanguage;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/09/12 14:16
 */
@Data
@TableName("oum_position")
public class PositionPO extends DefaultPO {

	private String departmentId;

	private String code;

	private Integer member;

	private String remark;

	@TableField(exist = false)
	private String name;

	@TableField(exist = false)
	private String departmentName;

	@TableField(exist = false)
	private List<Integer> departmentIds;

	@TableField(exist = false)
	private String language;

	@TableField(exist = false)
	private List<Integer> ids;

	@TableField(exist = false)
	private Integer pageNum;

	@TableField(exist = false)
	private Integer pageSize;

	@TableField(exist = false)
	private Integer userId;

	@TableField(exist = false)
	private String userCode;

	@TableField(exist = false)
	private String userName;

	@TableField(exist = false)
	private LocalDateTime addedTime;

	@TableField(exist = false)
	private List<MultiLanguage> names;

}
