package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;
import lombok.Data;

/**
 * 参数配置 PO.
 *
 * <AUTHOR>
 */
@Data
@TableName("sys_param_config")
public class ParamConfigPO extends DefaultPO {

	/**
	 * 参数名称
	 */
	private String paramName;

	/**
	 * 参数键
	 */
	private String paramKey;

	/**
	 * 参数值
	 */
	private String paramValue;

	/**
	 * 备注
	 */
	private String remark;

}
