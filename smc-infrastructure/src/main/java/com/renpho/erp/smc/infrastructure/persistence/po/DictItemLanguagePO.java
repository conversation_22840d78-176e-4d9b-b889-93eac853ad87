package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("sys_dict_item_language")
public class DictItemLanguagePO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> {

	private Integer dictItemId;

	private String language;

	private String name;

}
