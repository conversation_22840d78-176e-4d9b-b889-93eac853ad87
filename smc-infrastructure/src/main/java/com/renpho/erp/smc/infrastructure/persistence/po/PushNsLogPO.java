package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("sys_push_ns_log")
public class PushNsLogPO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> {

	private Integer type;

	private String reqBody;

	private String resBody;

	private String resultCode;

}