package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 字典类型 PO.
 *
 * <AUTHOR>
 */
@Data
@TableName("sys_dict_type_language")
public class DictTypeLanguagePO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> {

	/**
	 * 类型id
	 */

	private Integer dictTypeId;

	/**
	 * 多语言
	 */
	private String language;

	/**
	 * 多语言名称
	 */
	private String name;

}
