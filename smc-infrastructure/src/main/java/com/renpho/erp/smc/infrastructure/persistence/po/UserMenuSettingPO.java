package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户菜单配置表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_user_menu_setting")
public class UserMenuSettingPO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> implements Serializable {

	/**
	 * 用户ID
	 */
	@TableField(value = "user_id")
	private Integer userId;

	/**
	 * 菜单Id
	 */
	@TableField(value = "menu_id")
	private Integer menuId;

	@TableField(value = "path")
	private String path;

	@TableField(value = "menu_status")
	private Integer menuStatus;

	@Serial
	private static final long serialVersionUID = 1L;

}