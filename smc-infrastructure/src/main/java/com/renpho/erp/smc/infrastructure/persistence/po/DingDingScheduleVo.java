package com.renpho.erp.smc.infrastructure.persistence.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 钉钉日程
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class DingDingScheduleVo implements Serializable {

	/**
	 * 开始时间
	 */
	private LocalDateTime startTime;

	/**
	 * 结束时间
	 */
	private LocalDateTime endTime;

	/**
	 * 内容
	 */
	private String content;

	/**
	 * 地点
	 */
	private String venue;

	/**
	 * 日期
	 */
	private LocalDate day;

	/**
	 * erp系统id
	 */
	private Integer userId;

	/**
	 * 钉钉用户id
	 */
	private String dingDingUserId;

	/**
	 * 钉钉用户id
	 */
	private String status;

	/**
	 * 是否全天
	 */
	private String isAllDay;

	@Serial
	private static final long serialVersionUID = 1L;

}