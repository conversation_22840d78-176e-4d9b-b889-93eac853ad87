package com.renpho.erp.smc.infrastructure.persistence.po;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("sys_dict_item")
public class DictItemPO extends DefaultPO {

	private Integer dictId;

	private String dictType;

	private String dictKey;

	private String multiLanguageNames;

	private String remark;

	private Integer sort;

	@Deprecated
	private Integer isDeleted;

	@Deprecated
	@TableField(exist = false)
	private String language;

	@TableField(exist = false)
	private String name;

	@TableField(exist = false)
	private List<DictItemLanguagePO> names;

}
