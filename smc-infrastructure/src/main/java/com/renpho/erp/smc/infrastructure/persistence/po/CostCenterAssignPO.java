package com.renpho.erp.smc.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.Data;

import java.time.LocalDateTime;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@TableName("oum_cost_center_assign")
public class CostCenterAssignPO extends AlterationPO<Integer, Integer, LocalDateTime, Integer> {

	/**
	 * 用户ID
	 */
	private Integer userId;

	/**
	 * 成本中心ID
	 */
	private Integer costCenterId;

	/**
	 * 生效日期
	 */
	private LocalDateTime efficetiveDate;

	/**
	 * 成本中心名称
	 */
	@TableField(exist = false)
	private String costCenterName;

	/**
	 * 岗位名称
	 */
	@TableField(exist = false)
	private String positionName;

	/**
	 * 主部门名称
	 */
	@TableField(exist = false)
	private String mainDepartmentName;

	/**
	 * 用户名称
	 */
	@TableField(exist = false)
	private String userName;

	/**
	 * 用户工号
	 */
	@TableField(exist = false)
	private String userCode;

	/**
	 * 员工ids，多选可搜索，选项要求参考“通用说明”页面的U01
	 */
	@TableField(exist = false)
	private List<Integer> userIds;

	/**
	 * 员工状态，1 在职，0 离职
	 */
	@TableField(exist = false)
	private Integer staffStatus;

	/**
	 * 部门ids
	 * 多选可搜索，选项要求参考“通用说明”页面的D01，部门与用户的关系来自部门管理界面（即一个用户会有多个部门），多部门用户会出现所选部门与搜出来的部门不一致的情况。
	 */
	@TableField(exist = false)
	private List<Integer> departmentIds;

	/**
	 * 成本中心ids，多选可搜索
	 */
	@TableField(exist = false)
	private List<Integer> costCenterIds;

	/**
	 * 生效日期，日期范围搜索,搜索范围包括开始日期和结束日期
	 */
	@TableField(exist = false)
	private String startEfficetiveDate;

	/**
	 * 生效日期，日期范围搜索,搜索范围包括开始日期和结束日期
	 */
	@TableField(exist = false)
	private String endEfficetiveDate;

}